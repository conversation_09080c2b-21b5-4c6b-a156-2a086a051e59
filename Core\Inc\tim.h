/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    tim.h
  * @brief   This file contains all the function prototypes for
  *          the tim.c file
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __TIM_H__
#define __TIM_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"

/* USER CODE BEGIN Includes */
// An highlighted block
/* 锟斤拷时锟斤拷锟截碉拷锟斤拷锟斤拷 */

/* USER CODE END Includes */

extern TIM_HandleTypeDef htim14;

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

void MX_TIM14_Init(void);

/* USER CODE BEGIN Prototypes */
extern volatile int propulsionCommand;
#define  NUM_PROPULSORS 8
extern uint8_t propulsor_ids[NUM_PROPULSORS];
extern uint32_t current_can_id;
extern int current_speed;
/* USER CODE END Prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __TIM_H__ */

