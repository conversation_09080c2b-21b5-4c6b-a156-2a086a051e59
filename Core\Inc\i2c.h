/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    i2c.h
  * @brief   This file contains all the function prototypes for
  *          the i2c.c file
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __I2C_H__
#define __I2C_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"

/* USER CODE BEGIN Includes */
#define AT24C08_ADDR_W 0xA0  //器件地址+写标志位
#define AT24C08_ADDR_R 0xA1  //器件地址+读标志位 

//添加超时定义
#define IIC_TIMEOUT_MS 1000  // 超时时间，单位为毫秒
/* USER CODE END Includes */

extern I2C_HandleTypeDef hi2c1;

/* USER CODE BEGIN Private defines */
#define BufferSize 256

/* USER CODE END Private defines */

void MX_I2C1_Init(void);

/* USER CODE BEGIN Prototypes */
void IIC_Start(void);					//IIC的起始信号
void IIC_Stop(void);					//IIC停止信号
void IIC_WriteData(uint8_t data);//IIC写数据
void IIC_WriteAddr(uint8_t adrr);	//IIC写地址
uint8_t IIC_readData(void);				//IIC读数据
void AT24C08_WriteOneByte(uint8_t addr,uint8_t data);//EEPOM写单个字节
uint8_t AT24C08_ReadOneByte(uint8_t addr);						//EEPOm读单个字节
void AT24C08_ReadData(uint8_t addr, uint8_t *data, uint16_t length);//写数据
void AT24C08_WriteData(uint8_t addr, uint8_t *data, uint16_t length);//读数据
HAL_StatusTypeDef Write_CAN_IDs_To_EEPROM(void);// 写入CAN ID到EEPROM
void Read_CAN_IDs_From_EEPROM(uint8_t start_index, uint8_t end_index);// 从EEPROM读取CAN ID
HAL_StatusTypeDef Update_Single_CAN_ID_In_EEPROM(uint8_t index, uint16_t new_id);
/* USER CODE END Prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __I2C_H__ */

