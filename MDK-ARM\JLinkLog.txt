T2BFC 000:051.113   SEGGER J-Link V6.86 Log File
T2BFC 000:051.357   DLL Compiled: Sep 24 2020 17:31:31
T2BFC 000:051.371   Logging started @ 2025-01-21 03:33
T2BFC 000:051.399 - 51.405ms
T2BFC 000:051.420 JLINK_SetWarnOutHandler(...)
T2BFC 000:051.434 - 0.019ms
T2BFC 000:051.448 JLINK_OpenEx(...)
T2BFC 000:052.970   Firmware: J-Link ARM-OB STM32 compiled Aug 22 2012 19:52:04
T2BFC 000:056.290   Hardware: V7.00
T2BFC 000:056.342   S/N: 20090929
T2BFC 000:056.364   OEM: SEGGER
T2BFC 000:056.383   Feature(s): RDI,FlashDL,FlashBP,JFlash,GDBFull
T2BFC 000:058.128   TELNET listener socket opened on port 19021
T2BFC 000:058.275   WEBSRV Starting webserver
T2BFC 000:058.550   WEBSRV Webserver running on local port 19080
T2BFC 000:058.583 - 7.143ms returns "O.K."
T2BFC 000:058.623 JLINK_GetEmuCaps()
T2BFC 000:058.642 - 0.027ms returns 0x88EA5833
T2BFC 000:058.677 JLINK_TIF_GetAvailable(...)
T2BFC 000:059.108 - 0.447ms
T2BFC 000:059.138 JLINK_SetErrorOutHandler(...)
T2BFC 000:059.152 - 0.020ms
T2BFC 000:059.187 JLINK_ExecCommand("ProjectFile = "D:\stm\BSP_CAN_test\MDK-ARM\JLinkSettings.ini"", ...). 
T2BFC 000:070.665 - 11.497ms returns 0x00
T2BFC 000:070.722 JLINK_ExecCommand("Device = STM32F407ZETx", ...). 
T2BFC 000:071.334   Device "STM32F407ZE" selected.
T2BFC 000:071.834 - 1.105ms returns 0x00
T2BFC 000:071.866 JLINK_GetHardwareVersion()
T2BFC 000:071.879 - 0.019ms returns 70000
T2BFC 000:071.902 JLINK_GetDLLVersion()
T2BFC 000:071.915 - 0.031ms returns 68600
T2BFC 000:071.942 JLINK_GetOEMString(...)
T2BFC 000:071.957 JLINK_GetFirmwareString(...)
T2BFC 000:071.969 - 0.017ms
T2BFC 000:072.002 JLINK_GetDLLVersion()
T2BFC 000:072.014 - 0.017ms returns 68600
T2BFC 000:072.029 JLINK_GetCompileDateTime()
T2BFC 000:072.040 - 0.017ms
T2BFC 000:072.061 JLINK_GetFirmwareString(...)
T2BFC 000:072.073 - 0.017ms
T2BFC 000:072.092 JLINK_GetHardwareVersion()
T2BFC 000:072.104 - 0.017ms returns 70000
T2BFC 000:072.123 JLINK_GetSN()
T2BFC 000:072.135 - 0.017ms returns 20090929
T2BFC 000:072.154 JLINK_GetOEMString(...)
T2BFC 000:072.180 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T2BFC 000:074.128 - 1.976ms returns 0x00
T2BFC 000:074.170 JLINK_HasError()
T2BFC 000:074.198 JLINK_SetSpeed(5000)
T2BFC 000:074.702 - 0.533ms
T2BFC 000:074.744 JLINK_GetId()
T2BFC 000:077.609   Found SW-DP with ID 0x2BA01477
T2BFC 000:122.832   Found SW-DP with ID 0x2BA01477
T2BFC 000:132.508   Old FW that does not support reading DPIDR via DAP jobs
T2BFC 000:145.326   Unknown DP version. Assuming DPv0
T2BFC 000:145.375   Scanning AP map to find all available APs
T2BFC 000:153.561   AP[1]: Stopped AP scan as end of AP map has been reached
T2BFC 000:153.595   AP[0]: AHB-AP (IDR: 0x24770011)
T2BFC 000:153.619   Iterating through AP map to find AHB-AP to use
T2BFC 000:167.428   AP[0]: Core found
T2BFC 000:167.462   AP[0]: AHB-AP ROM base: 0xE00FF000
T2BFC 000:174.323   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T2BFC 000:174.358   Found Cortex-M4 r0p1, Little endian.
T2BFC 000:277.322   -- Max. mem block: 0x00002C18
T2BFC 000:277.385   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2BFC 000:278.814   CPU_ReadMem(4 bytes @ 0xE0002000)
T2BFC 000:279.777   FPUnit: 6 code (BP) slots and 2 literal slots
T2BFC 000:279.811   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T2BFC 000:280.709   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2BFC 000:281.677   CPU_ReadMem(4 bytes @ 0xE0001000)
T2BFC 000:282.747   CPU_WriteMem(4 bytes @ 0xE0001000)
T2BFC 000:283.726   CPU_ReadMem(4 bytes @ 0xE000ED88)
T2BFC 000:284.535   CPU_WriteMem(4 bytes @ 0xE000ED88)
T2BFC 000:285.575   CPU_ReadMem(4 bytes @ 0xE000ED88)
T2BFC 000:286.597   CPU_WriteMem(4 bytes @ 0xE000ED88)
T2BFC 000:287.608   CoreSight components:
T2BFC 000:287.646   ROMTbl[0] @ E00FF000
T2BFC 000:287.670   CPU_ReadMem(64 bytes @ 0xE00FF000)
T2BFC 000:289.342   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T2BFC 000:290.577   ROMTbl[0][0]: E000E000, CID: B105E00D, PID: 000BB00C SCS-M7
T2BFC 000:290.609   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T2BFC 000:291.827   ROMTbl[0][1]: E0001000, CID: B105E00D, PID: 003BB002 DWT
T2BFC 000:291.898   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T2BFC 000:293.256   ROMTbl[0][2]: E0002000, CID: B105E00D, PID: 002BB003 FPB
T2BFC 000:293.284   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T2BFC 000:294.532   ROMTbl[0][3]: ********, CID: B105E00D, PID: 003BB001 ITM
T2BFC 000:294.560   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T2BFC 000:295.794   ROMTbl[0][4]: ********, CID: B105900D, PID: 000BB9A1 TPIU
T2BFC 000:295.843   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T2BFC 000:296.826   ROMTbl[0][5]: ********, CID: B105900D, PID: 000BB925 ETM
T2BFC 000:297.968 - 223.239ms returns 0x2BA01477
T2BFC 000:298.001 JLINK_GetDLLVersion()
T2BFC 000:298.014 - 0.019ms returns 68600
T2BFC 000:298.030 JLINK_CORE_GetFound()
T2BFC 000:298.042 - 0.018ms returns 0xE0000FF
T2BFC 000:298.060 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T2BFC 000:298.074   Value=0xE00FF000
T2BFC 000:298.092 - 0.038ms returns 0
T2BFC 000:298.202 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T2BFC 000:298.224   Value=0xE00FF000
T2BFC 000:298.243 - 0.048ms returns 0
T2BFC 000:298.260 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T2BFC 000:298.273   Value=0x********
T2BFC 000:298.292 - 0.038ms returns 0
T2BFC 000:298.316 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
T2BFC 000:298.358   CPU_ReadMem(32 bytes @ 0xE0041FD0)
T2BFC 000:299.586   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T2BFC 000:299.613 - 1.303ms returns 32 (0x20)
T2BFC 000:299.632 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T2BFC 000:299.647   Value=0x00000000
T2BFC 000:299.683 - 0.058ms returns 0
T2BFC 000:299.700 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T2BFC 000:299.715   Value=0x********
T2BFC 000:299.733 - 0.039ms returns 0
T2BFC 000:299.748 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T2BFC 000:299.760   Value=0x********
T2BFC 000:299.778 - 0.036ms returns 0
T2BFC 000:299.793 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T2BFC 000:299.806   Value=0xE0001000
T2BFC 000:299.831 - 0.044ms returns 0
T2BFC 000:299.846 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T2BFC 000:299.859   Value=0xE0002000
T2BFC 000:299.877 - 0.036ms returns 0
T2BFC 000:299.892 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T2BFC 000:299.904   Value=0xE000E000
T2BFC 000:299.922 - 0.036ms returns 0
T2BFC 000:299.937 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T2BFC 000:299.950   Value=0xE000EDF0
T2BFC 000:299.967 - 0.036ms returns 0
T2BFC 000:299.982 JLINK_GetDebugInfo(0x01 = Unknown)
T2BFC 000:299.995   Value=0x00000001
T2BFC 000:300.013 - 0.036ms returns 0
T2BFC 000:300.028 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T2BFC 000:300.045   CPU_ReadMem(4 bytes @ 0xE000ED00)
T2BFC 000:301.101   Data:  41 C2 0F 41
T2BFC 000:301.128   Debug reg: CPUID
T2BFC 000:301.146 - 1.125ms returns 1 (0x1)
T2BFC 000:301.165 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T2BFC 000:301.178   Value=0x00000000
T2BFC 000:301.197 - 0.037ms returns 0
T2BFC 000:301.212 JLINK_HasError()
T2BFC 000:301.228 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T2BFC 000:301.241 - 0.019ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T2BFC 000:301.256 JLINK_Reset()
T2BFC 000:301.286   CPU is running
T2BFC 000:301.306   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T2BFC 000:302.267   CPU is running
T2BFC 000:302.294   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2BFC 000:303.337   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T2BFC 000:304.682   Reset: Reset device via AIRCR.SYSRESETREQ.
T2BFC 000:304.710   CPU is running
T2BFC 000:304.730   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T2BFC 000:358.940   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2BFC 000:360.057   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2BFC 000:361.080   CPU is running
T2BFC 000:361.109   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T2BFC 000:362.129   CPU is running
T2BFC 000:362.157   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2BFC 000:368.594   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2BFC 000:373.976   CPU_WriteMem(4 bytes @ 0xE0002000)
T2BFC 000:375.054   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T2BFC 000:376.050   CPU_ReadMem(4 bytes @ 0xE0001000)
T2BFC 000:377.057 - 75.815ms
T2BFC 000:377.090 JLINK_HasError()
T2BFC 000:377.107 JLINK_ReadReg(R15 (PC))
T2BFC 000:377.130 - 0.030ms returns 0x080001A0
T2BFC 000:377.148 JLINK_ReadReg(XPSR)
T2BFC 000:377.162 - 0.019ms returns 0x01000000
T2BFC 000:377.178 JLINK_Halt()
T2BFC 000:377.190 - 0.018ms returns 0x00
T2BFC 000:377.208 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T2BFC 000:377.225   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2BFC 000:378.290   Data:  03 00 03 00
T2BFC 000:378.317   Debug reg: DHCSR
T2BFC 000:378.336 - 1.134ms returns 1 (0x1)
T2BFC 000:378.364 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T2BFC 000:378.381   Debug reg: DHCSR
T2BFC 000:378.416   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T2BFC 000:379.401 - 1.051ms returns 0 (0x00000000)
T2BFC 000:379.434 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T2BFC 000:379.452   Debug reg: DEMCR
T2BFC 000:379.476   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2BFC 000:380.533 - 1.115ms returns 0 (0x00000000)
T2BFC 000:380.712 JLINK_GetHWStatus(...)
T2BFC 000:381.336 - 0.638ms returns 0
T2BFC 000:381.388 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T2BFC 000:381.403 - 0.022ms returns 0x06
T2BFC 000:381.419 JLINK_GetNumBPUnits(Type = 0xF0)
T2BFC 000:381.436 - 0.022ms returns 0x2000
T2BFC 000:381.451 JLINK_GetNumWPUnits()
T2BFC 000:381.463 - 0.018ms returns 4
T2BFC 000:381.493 JLINK_GetSpeed()
T2BFC 000:381.506 - 0.019ms returns 4000
T2BFC 000:381.531 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T2BFC 000:381.549   CPU_ReadMem(4 bytes @ 0xE000E004)
T2BFC 000:382.492   Data:  02 00 00 00
T2BFC 000:382.525 - 1.000ms returns 1 (0x1)
T2BFC 000:382.544 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T2BFC 000:382.562   CPU_ReadMem(4 bytes @ 0xE000E004)
T2BFC 000:383.481   Data:  02 00 00 00
T2BFC 000:383.509 - 0.972ms returns 1 (0x1)
T2BFC 000:383.528 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T2BFC 000:383.542   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T2BFC 000:384.373   CPU_WriteMem(28 bytes @ 0xE0001000)
T2BFC 000:385.555 - 2.044ms returns 0x1C
T2BFC 000:385.587 JLINK_HasError()
T2BFC 000:385.604 JLINK_ReadReg(R15 (PC))
T2BFC 000:385.625 - 0.027ms returns 0x080001A0
T2BFC 000:385.641 JLINK_ReadReg(XPSR)
T2BFC 000:385.654 - 0.019ms returns 0x01000000
T2BFC 000:390.524 JLINK_ReadMemEx(0xE0001004, 0x4 Bytes, Flags = 0x02000000)
T2BFC 000:390.559   Data:  00 00 00 00
T2BFC 000:390.580   Debug reg: DWT_CYCCNT
T2BFC 000:390.598 - 0.080ms returns 4 (0x4)
T2BFC 000:502.048 JLINK_HasError()
T2BFC 000:502.092 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T2BFC 000:502.110 - 0.032ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T2BFC 000:502.141 JLINK_Reset()
T2BFC 000:502.174   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T2BFC 000:503.119   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2BFC 000:504.148   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T2BFC 000:505.315   Reset: Reset device via AIRCR.SYSRESETREQ.
T2BFC 000:505.350   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T2BFC 000:558.874   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2BFC 000:559.595   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2BFC 000:560.318   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T2BFC 000:561.015   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2BFC 000:566.945   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2BFC 000:571.301   CPU_WriteMem(4 bytes @ 0xE0002000)
T2BFC 000:572.030   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T2BFC 000:572.754   CPU_ReadMem(4 bytes @ 0xE0001000)
T2BFC 000:573.456 - 71.328ms
T2BFC 000:573.542 JLINK_HasError()
T2BFC 000:573.568 JLINK_ReadReg(R15 (PC))
T2BFC 000:573.585 - 0.023ms returns 0x080001A0
T2BFC 000:573.601 JLINK_ReadReg(XPSR)
T2BFC 000:573.615 - 0.019ms returns 0x01000000
T2BFC 000:574.153 JLINK_ReadMemEx(0x080001A0, 0x3C Bytes, Flags = 0x02000000)
T2BFC 000:574.178   CPU_ReadMem(128 bytes @ 0x08000180)
T2BFC 000:576.191    -- Updating C cache (128 bytes @ 0x08000180)
T2BFC 000:576.226    -- Read from C cache (60 bytes @ 0x080001A0)
T2BFC 000:576.247   Data:  06 48 80 47 06 48 00 47 FE E7 FE E7 FE E7 FE E7 ...
T2BFC 000:576.266 - 2.120ms returns 60 (0x3C)
T2BFC 000:576.285 JLINK_ReadMemEx(0x080001A0, 0x2 Bytes, Flags = 0x02000000)
T2BFC 000:576.301    -- Read from C cache (2 bytes @ 0x080001A0)
T2BFC 000:576.319   Data:  06 48
T2BFC 000:576.338 - 0.059ms returns 2 (0x2)
T2BFC 000:576.451 JLINK_ReadMemEx(0x080001A2, 0x2 Bytes, Flags = 0x02000000)
T2BFC 000:576.466    -- Read from C cache (2 bytes @ 0x080001A2)
T2BFC 000:576.484   Data:  80 47
T2BFC 000:576.502 - 0.058ms returns 2 (0x2)
T2BFC 000:576.527 JLINK_ReadMemEx(0x080001A2, 0x2 Bytes, Flags = 0x02000000)
T2BFC 000:576.541    -- Read from C cache (2 bytes @ 0x080001A2)
T2BFC 000:576.560   Data:  80 47
T2BFC 000:576.578 - 0.056ms returns 2 (0x2)
T2BFC 000:576.593 JLINK_ReadMemEx(0x080001A4, 0x3C Bytes, Flags = 0x02000000)
T2BFC 000:576.607    -- Read from C cache (60 bytes @ 0x080001A4)
T2BFC 000:576.626   Data:  06 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T2BFC 000:576.644 - 0.056ms returns 60 (0x3C)
T2BFC 000:576.659 JLINK_ReadMemEx(0x080001A4, 0x2 Bytes, Flags = 0x02000000)
T2BFC 000:576.672    -- Read from C cache (2 bytes @ 0x080001A4)
T2BFC 000:576.690   Data:  06 48
T2BFC 000:576.709 - 0.055ms returns 2 (0x2)
T2BFC 002:344.833 JLINK_HasError()
T2BFC 002:344.866 JLINK_ReadReg(R0)
T2BFC 002:345.867 - 1.015ms returns 0x00000000
T2BFC 002:345.895 JLINK_ReadReg(R1)
T2BFC 002:345.909 - 0.020ms returns 0x00000000
T2BFC 002:345.926 JLINK_ReadReg(R2)
T2BFC 002:345.939 - 0.019ms returns 0x00000000
T2BFC 002:345.956 JLINK_ReadReg(R3)
T2BFC 002:345.969 - 0.019ms returns 0x00000000
T2BFC 002:345.984 JLINK_ReadReg(R4)
T2BFC 002:345.997 - 0.019ms returns 0x00000000
T2BFC 002:346.012 JLINK_ReadReg(R5)
T2BFC 002:346.025 - 0.018ms returns 0x00000000
T2BFC 002:346.040 JLINK_ReadReg(R6)
T2BFC 002:346.052 - 0.018ms returns 0x00000000
T2BFC 002:346.068 JLINK_ReadReg(R7)
T2BFC 002:346.080 - 0.019ms returns 0x00000000
T2BFC 002:346.095 JLINK_ReadReg(R8)
T2BFC 002:346.120 - 0.031ms returns 0x00000000
T2BFC 002:346.136 JLINK_ReadReg(R9)
T2BFC 002:346.148 - 0.018ms returns 0x00000000
T2BFC 002:346.163 JLINK_ReadReg(R10)
T2BFC 002:346.176 - 0.018ms returns 0x00000000
T2BFC 002:346.191 JLINK_ReadReg(R11)
T2BFC 002:346.206 - 0.023ms returns 0x00000000
T2BFC 002:346.233 JLINK_ReadReg(R12)
T2BFC 002:346.253 - 0.025ms returns 0x00000000
T2BFC 002:346.269 JLINK_ReadReg(R13 (SP))
T2BFC 002:346.282 - 0.019ms returns 0x20000588
T2BFC 002:346.298 JLINK_ReadReg(R14)
T2BFC 002:346.310 - 0.019ms returns 0xFFFFFFFF
T2BFC 002:346.326 JLINK_ReadReg(R15 (PC))
T2BFC 002:346.338 - 0.019ms returns 0x080001A0
T2BFC 002:346.354 JLINK_ReadReg(XPSR)
T2BFC 002:346.367 - 0.019ms returns 0x01000000
T2BFC 002:346.382 JLINK_ReadReg(MSP)
T2BFC 002:346.395 - 0.019ms returns 0x20000588
T2BFC 002:346.410 JLINK_ReadReg(PSP)
T2BFC 002:346.423 - 0.019ms returns 0x00000000
T2BFC 002:346.438 JLINK_ReadReg(CFBP)
T2BFC 002:346.451 - 0.019ms returns 0x00000000
T2BFC 002:346.467 JLINK_ReadReg(FPSCR)
T2BFC 002:352.270 - 5.819ms returns 0x00000000
T2BFC 002:352.299 JLINK_ReadReg(FPS0)
T2BFC 002:352.314 - 0.021ms returns 0x00000000
T2BFC 002:352.330 JLINK_ReadReg(FPS1)
T2BFC 002:352.343 - 0.019ms returns 0x00000000
T2BFC 002:352.358 JLINK_ReadReg(FPS2)
T2BFC 002:352.371 - 0.018ms returns 0x00000000
T2BFC 002:352.386 JLINK_ReadReg(FPS3)
T2BFC 002:352.399 - 0.018ms returns 0x00000000
T2BFC 002:352.414 JLINK_ReadReg(FPS4)
T2BFC 002:352.427 - 0.019ms returns 0x00000000
T2BFC 002:352.442 JLINK_ReadReg(FPS5)
T2BFC 002:352.454 - 0.018ms returns 0x00000000
T2BFC 002:352.470 JLINK_ReadReg(FPS6)
T2BFC 002:352.482 - 0.019ms returns 0x00000000
T2BFC 002:352.498 JLINK_ReadReg(FPS7)
T2BFC 002:352.510 - 0.019ms returns 0x00000000
T2BFC 002:352.525 JLINK_ReadReg(FPS8)
T2BFC 002:352.538 - 0.018ms returns 0x00000000
T2BFC 002:352.553 JLINK_ReadReg(FPS9)
T2BFC 002:352.566 - 0.018ms returns 0x00000000
T2BFC 002:352.581 JLINK_ReadReg(FPS10)
T2BFC 002:352.637 - 0.062ms returns 0x00000000
T2BFC 002:352.653 JLINK_ReadReg(FPS11)
T2BFC 002:352.666 - 0.018ms returns 0x00000000
T2BFC 002:352.681 JLINK_ReadReg(FPS12)
T2BFC 002:352.693 - 0.018ms returns 0x00000000
T2BFC 002:352.709 JLINK_ReadReg(FPS13)
T2BFC 002:352.721 - 0.018ms returns 0x00000000
T2BFC 002:352.736 JLINK_ReadReg(FPS14)
T2BFC 002:352.749 - 0.018ms returns 0x00000000
T2BFC 002:352.764 JLINK_ReadReg(FPS15)
T2BFC 002:352.777 - 0.018ms returns 0x00000000
T2BFC 002:352.792 JLINK_ReadReg(FPS16)
T2BFC 002:352.804 - 0.018ms returns 0x00000000
T2BFC 002:352.819 JLINK_ReadReg(FPS17)
T2BFC 002:352.832 - 0.018ms returns 0x00000000
T2BFC 002:352.847 JLINK_ReadReg(FPS18)
T2BFC 002:352.859 - 0.018ms returns 0x00000000
T2BFC 002:352.874 JLINK_ReadReg(FPS19)
T2BFC 002:352.887 - 0.018ms returns 0x00000000
T2BFC 002:352.902 JLINK_ReadReg(FPS20)
T2BFC 002:352.914 - 0.018ms returns 0x00000000
T2BFC 002:352.930 JLINK_ReadReg(FPS21)
T2BFC 002:352.942 - 0.018ms returns 0x00000000
T2BFC 002:352.957 JLINK_ReadReg(FPS22)
T2BFC 002:352.970 - 0.018ms returns 0x00000000
T2BFC 002:352.985 JLINK_ReadReg(FPS23)
T2BFC 002:352.998 - 0.018ms returns 0x00000000
T2BFC 002:353.013 JLINK_ReadReg(FPS24)
T2BFC 002:353.025 - 0.018ms returns 0x00000000
T2BFC 002:353.040 JLINK_ReadReg(FPS25)
T2BFC 002:353.053 - 0.018ms returns 0x00000000
T2BFC 002:353.068 JLINK_ReadReg(FPS26)
T2BFC 002:353.081 - 0.018ms returns 0x00000000
T2BFC 002:353.096 JLINK_ReadReg(FPS27)
T2BFC 002:353.108 - 0.018ms returns 0x00000000
T2BFC 002:353.123 JLINK_ReadReg(FPS28)
T2BFC 002:353.136 - 0.018ms returns 0x00000000
T2BFC 002:353.151 JLINK_ReadReg(FPS29)
T2BFC 002:353.164 - 0.018ms returns 0x00000000
T2BFC 002:353.179 JLINK_ReadReg(FPS30)
T2BFC 002:353.194 - 0.021ms returns 0x00000000
T2BFC 002:353.209 JLINK_ReadReg(FPS31)
T2BFC 002:353.222 - 0.018ms returns 0x00000000
T15FC 002:585.991 JLINK_ReadMemEx(0x080001A0, 0x2 Bytes, Flags = 0x02000000)
T15FC 002:586.032    -- Read from C cache (2 bytes @ 0x080001A0)
T15FC 002:586.053   Data:  06 48
T15FC 002:586.072 - 0.087ms returns 2 (0x2)
T15FC 002:586.090 JLINK_HasError()
T15FC 002:586.108 JLINK_SetBPEx(Addr = 0x080027C4, Type = 0xFFFFFFF2)
T15FC 002:586.131 - 0.036ms returns 0x00000001
T15FC 002:586.163 JLINK_HasError()
T15FC 002:586.186 JLINK_SetBPEx(Addr = 0x08002210, Type = 0xFFFFFFF2)
T15FC 002:586.212 - 0.032ms returns 0x00000002
T15FC 002:586.229 JLINK_HasError()
T15FC 002:586.244 JLINK_SetBPEx(Addr = 0x0800222E, Type = 0xFFFFFFF2)
T15FC 002:586.258 - 0.019ms returns 0x00000003
T15FC 002:586.273 JLINK_HasError()
T15FC 002:586.288 JLINK_SetBPEx(Addr = 0x08002268, Type = 0xFFFFFFF2)
T15FC 002:586.301 - 0.019ms returns 0x00000004
T15FC 002:586.317 JLINK_HasError()
T15FC 002:586.332 JLINK_SetBPEx(Addr = 0x0800223E, Type = 0xFFFFFFF2)
T15FC 002:586.345 - 0.019ms returns 0x00000005
T15FC 002:586.360 JLINK_HasError()
T15FC 002:586.376 JLINK_SetBPEx(Addr = 0x08002258, Type = 0xFFFFFFF2)
T15FC 002:586.389 - 0.019ms returns 0x00000006
T15FC 002:586.404 JLINK_HasError()
T15FC 002:586.419 JLINK_SetBPEx(Addr = 0x080027F0, Type = 0xFFFFFFF2)
T15FC 002:586.435    -- BP[0] @ 0x080027C4 converted into FlashBP
T15FC 002:586.454 - 0.040ms returns 0x00000007
T15FC 002:586.469 JLINK_HasError()
T15FC 002:586.484 JLINK_SetBPEx(Addr = 0x080027F6, Type = 0xFFFFFFF2)
T15FC 002:586.498    -- BP[1] @ 0x08002210 converted into FlashBP
T15FC 002:586.517 - 0.039ms returns 0x00000008
T15FC 002:586.537 JLINK_HasError()
T15FC 002:586.566 JLINK_SetBPEx(Addr = 0x080027FE, Type = 0xFFFFFFF2)
T15FC 002:586.583    -- BP[2] @ 0x0800222E converted into FlashBP
T15FC 002:586.602 - 0.041ms returns 0x00000009
T15FC 002:586.617 JLINK_HasError()
T15FC 002:586.632 JLINK_SetBPEx(Addr = 0x0800280C, Type = 0xFFFFFFF2)
T15FC 002:586.646    -- BP[3] @ 0x08002268 converted into FlashBP
T15FC 002:586.664 - 0.038ms returns 0x0000000A
T15FC 002:586.680 JLINK_HasError()
T15FC 002:586.695 JLINK_SetBPEx(Addr = 0x08002814, Type = 0xFFFFFFF2)
T15FC 002:586.709    -- BP[4] @ 0x0800223E converted into FlashBP
T15FC 002:586.732 - 0.043ms returns 0x0000000B
T15FC 002:586.747 JLINK_HasError()
T15FC 002:586.762 JLINK_SetBPEx(Addr = 0x08002222, Type = 0xFFFFFFF2)
T15FC 002:586.776    -- BP[5] @ 0x08002258 converted into FlashBP
T15FC 002:586.795 - 0.038ms returns 0x0000000C
T15FC 002:586.810 JLINK_HasError()
T15FC 002:586.825 JLINK_HasError()
T15FC 002:586.841 JLINK_Go()
T15FC 002:586.857    -- Read from C cache (2 bytes @ 0x080001A0)
T15FC 002:586.878   CPU_ReadMem(4 bytes @ 0xE000ED18)
T15FC 002:587.701   CPU_WriteMem(4 bytes @ 0xE000ED18)
T15FC 002:588.474   CPU_ReadMem(4 bytes @ 0xE000ED18)
T15FC 002:589.236   CPU_WriteMem(4 bytes @ 0xE000ED18)
T15FC 002:589.962    -- Read from C cache (4 bytes @ 0x080001BC)
T15FC 002:590.014   -- Simulated
T15FC 002:590.044    -- Read from C cache (2 bytes @ 0x080001A2)
T15FC 002:590.074   -- Simulated
T15FC 002:590.108   CPU_ReadMem(64 bytes @ 0x08001F40)
T15FC 002:594.334    -- Updating C cache (64 bytes @ 0x08001F40)
T15FC 002:594.386    -- Read from C cache (2 bytes @ 0x08001F48)
T15FC 002:594.421    -- Read from C cache (4 bytes @ 0x08001F54)
T15FC 002:594.452   -- Simulated
T15FC 002:594.483    -- Read from C cache (2 bytes @ 0x08001F4A)
T15FC 002:594.514   -- Not simulated
T15FC 002:594.550   CPU_ReadMem(4 bytes @ 0xE000ED90)
T15FC 002:595.216   CPU_ReadMem(4 bytes @ 0xE000ED94)
T15FC 002:595.906    -- Start of preparing flash programming
T15FC 002:595.991    -- Calculating RAM usage
T15FC 002:596.188    -- RAM usage = 10012 Bytes
T15FC 002:596.220    -- Preserving CPU registers
T15FC 002:596.277    -- Preparing memory
T15FC 002:596.308    -- Preparing target
T15FC 002:601.771    -- Preserving target RAM temporarily used for programming
T15FC 002:703.383    -- Downloading RAMCode
T15FC 002:734.204    -- Preparing RAMCode
T15FC 002:740.671    -- Checking target RAM
T15FC 002:743.238    -- End of preparing flash programming
T15FC 002:743.300   CPU_ReadMem(16384 bytes @ 0x08000000)
T15FC 002:908.566    -- Updating flash cache (16384 bytes @ 0x08000000)
T15FC 002:908.812    -- Programming range 0x08000000 - 0x08003FFF (  1 Sector, 16 KB)
T15FC 002:909.062   Looking for J-Link GUI Server exe at: D:\keil\ARM\Segger\JLinkGUIServer.exe
T15FC 002:909.215   Looking for J-Link GUI Server exe at: D:\chuankou\JLink_V640\\JLinkGUIServer.exe
T15FC 003:210.910   Failed to connect to J-Link GUI Server.
T15FC 003:726.111    -- Start of restoring
T15FC 003:726.231    -- Restoring RAMCode
T15FC 003:731.936    -- Restoring target memory
T15FC 003:844.759    -- Restore target
T15FC 003:849.907    -- Restore memory
T15FC 003:849.936    -- Restoring CPU registers
T15FC 003:849.968    -- End of restoring
T15FC 003:852.299   CPU_WriteMem(4 bytes @ 0xE0002000)
T15FC 003:853.042   CPU_ReadMem(4 bytes @ 0xE0002008)
T15FC 003:853.757   CPU_WriteMem(4 bytes @ 0xE000EDF4)
T15FC 003:854.462   CPU_ReadMem(4 bytes @ 0xE000EDF8)
T15FC 003:855.105   CPU_WriteMem(4 bytes @ 0xE0002008)
T15FC 003:855.999   CPU_WriteMem(4 bytes @ 0xE000EDF8)
T15FC 003:857.100   CPU_WriteMem(4 bytes @ 0xE000EDF4)
T15FC 003:858.118   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T15FC 003:859.027   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T15FC 003:859.743   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T15FC 003:860.486   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T15FC 003:861.390   CPU_WriteMem(4 bytes @ 0xE0002008)
T15FC 003:862.365   CPU_WriteMem(4 bytes @ 0xE000EDF8)
T15FC 003:863.479   CPU_WriteMem(4 bytes @ 0xE000EDF4)
T15FC 003:864.607   CPU_WriteMem(4 bytes @ 0xE0002000)
T15FC 003:865.746   CPU_WriteMem(4 bytes @ 0xE0002000)
T15FC 003:866.451   CPU_WriteMem(4 bytes @ 0xE0002000)
T15FC 003:867.133   CPU_WriteMem(4 bytes @ 0xE0002000)
T15FC 003:867.947   CPU_WriteMem(4 bytes @ 0xE0002000)
T15FC 003:868.671   CPU_WriteMem(4 bytes @ 0xE0002000)
T15FC 003:869.755   CPU_ReadMem(4 bytes @ 0xE0001000)
T15FC 003:870.792   CPU_WriteMem(4 bytes @ 0xE0002008)
T15FC 003:870.821   CPU_WriteMem(4 bytes @ 0xE000200C)
T15FC 003:870.839   CPU_WriteMem(4 bytes @ 0xE0002010)
T15FC 003:870.858   CPU_WriteMem(4 bytes @ 0xE0002014)
T15FC 003:870.881   CPU_WriteMem(4 bytes @ 0xE0002018)
T15FC 003:870.900   CPU_WriteMem(4 bytes @ 0xE000201C)
T15FC 003:877.764 - 1290.953ms
T15FC 003:977.941 JLINK_HasError()
T15FC 003:977.996 JLINK_IsHalted()
T15FC 003:981.812 - 3.847ms returns TRUE
T15FC 003:981.859 JLINK_HasError()
T15FC 003:981.875 JLINK_Halt()
T15FC 003:981.888 - 0.019ms returns 0x00
T15FC 003:981.904 JLINK_IsHalted()
T15FC 003:981.916 - 0.019ms returns TRUE
T15FC 003:981.932 JLINK_IsHalted()
T15FC 003:981.944 - 0.018ms returns TRUE
T15FC 003:981.959 JLINK_IsHalted()
T15FC 003:981.971 - 0.018ms returns TRUE
T15FC 003:981.987 JLINK_HasError()
T15FC 003:982.003 JLINK_ReadReg(R15 (PC))
T15FC 003:982.019 - 0.022ms returns 0x080027C4
T15FC 003:982.034 JLINK_ReadReg(XPSR)
T15FC 003:982.048 - 0.019ms returns 0x61000000
T15FC 003:982.067 JLINK_HasError()
T15FC 003:982.083 JLINK_ClrBPEx(BPHandle = 0x00000001)
T15FC 003:982.098 - 0.021ms returns 0x00
T15FC 003:982.113 JLINK_HasError()
T15FC 003:982.128 JLINK_ClrBPEx(BPHandle = 0x00000002)
T15FC 003:982.141 - 0.019ms returns 0x00
T15FC 003:982.157 JLINK_HasError()
T15FC 003:982.172 JLINK_ClrBPEx(BPHandle = 0x00000003)
T15FC 003:982.185 - 0.019ms returns 0x00
T15FC 003:982.200 JLINK_HasError()
T15FC 003:982.215 JLINK_ClrBPEx(BPHandle = 0x00000004)
T15FC 003:982.228 - 0.019ms returns 0x00
T15FC 003:982.243 JLINK_HasError()
T15FC 003:982.258 JLINK_ClrBPEx(BPHandle = 0x00000005)
T15FC 003:982.271 - 0.019ms returns 0x00
T15FC 003:982.286 JLINK_HasError()
T15FC 003:982.301 JLINK_ClrBPEx(BPHandle = 0x00000006)
T15FC 003:982.314 - 0.019ms returns 0x00
T15FC 003:982.329 JLINK_HasError()
T15FC 003:982.344 JLINK_ClrBPEx(BPHandle = 0x00000007)
T15FC 003:982.357 - 0.019ms returns 0x00
T15FC 003:982.372 JLINK_HasError()
T15FC 003:982.387 JLINK_ClrBPEx(BPHandle = 0x00000008)
T15FC 003:982.400 - 0.019ms returns 0x00
T15FC 003:982.416 JLINK_HasError()
T15FC 003:982.430 JLINK_ClrBPEx(BPHandle = 0x00000009)
T15FC 003:982.443 - 0.019ms returns 0x00
T15FC 003:982.458 JLINK_HasError()
T15FC 003:982.473 JLINK_ClrBPEx(BPHandle = 0x0000000A)
T15FC 003:982.486 - 0.019ms returns 0x00
T15FC 003:982.501 JLINK_HasError()
T15FC 003:982.516 JLINK_ClrBPEx(BPHandle = 0x0000000B)
T15FC 003:982.529 - 0.019ms returns 0x00
T15FC 003:982.544 JLINK_HasError()
T15FC 003:982.559 JLINK_ClrBPEx(BPHandle = 0x0000000C)
T15FC 003:982.572 - 0.019ms returns 0x00
T15FC 003:982.587 JLINK_HasError()
T15FC 003:982.602 JLINK_HasError()
T15FC 003:982.618 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T15FC 003:982.639   CPU_ReadMem(4 bytes @ 0xE000ED30)
T15FC 003:983.746   Data:  03 00 00 00
T15FC 003:983.773 - 1.162ms returns 1 (0x1)
T15FC 003:983.809 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T15FC 003:983.841   CPU_ReadMem(4 bytes @ 0xE0001028)
T15FC 003:984.912   Data:  00 00 00 00
T15FC 003:984.944   Debug reg: DWT_FUNC[0]
T15FC 003:984.963 - 1.160ms returns 1 (0x1)
T15FC 003:984.984 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T15FC 003:985.003   CPU_ReadMem(4 bytes @ 0xE0001038)
T15FC 003:985.913   Data:  00 02 00 00
T15FC 003:985.957   Debug reg: DWT_FUNC[1]
T15FC 003:985.976 - 0.998ms returns 1 (0x1)
T15FC 003:985.996 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T15FC 003:986.015   CPU_ReadMem(4 bytes @ 0xE0001048)
T15FC 003:986.960   Data:  00 00 00 00
T15FC 003:986.987   Debug reg: DWT_FUNC[2]
T15FC 003:987.005 - 1.015ms returns 1 (0x1)
T15FC 003:987.026 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T15FC 003:987.045   CPU_ReadMem(4 bytes @ 0xE0001058)
T15FC 003:987.877   Data:  00 00 00 00
T15FC 003:987.920   Debug reg: DWT_FUNC[3]
T15FC 003:987.951 - 0.932ms returns 1 (0x1)
T15FC 003:988.034 JLINK_HasError()
T15FC 003:988.054 JLINK_ReadReg(R0)
T15FC 003:988.070 - 0.022ms returns 0x080027C5
T15FC 003:988.086 JLINK_ReadReg(R1)
T15FC 003:988.099 - 0.019ms returns 0x20000588
T15FC 003:988.115 JLINK_ReadReg(R2)
T15FC 003:988.128 - 0.019ms returns 0x00000000
T15FC 003:988.143 JLINK_ReadReg(R3)
T15FC 003:988.156 - 0.019ms returns 0x0800233D
T15FC 003:988.171 JLINK_ReadReg(R4)
T15FC 003:988.189 - 0.024ms returns 0x0800285C
T15FC 003:988.205 JLINK_ReadReg(R5)
T15FC 003:988.217 - 0.019ms returns 0x0800285C
T15FC 003:988.233 JLINK_ReadReg(R6)
T15FC 003:988.245 - 0.019ms returns 0x00000000
T15FC 003:988.261 JLINK_ReadReg(R7)
T15FC 003:988.273 - 0.019ms returns 0x00000000
T15FC 003:988.289 JLINK_ReadReg(R8)
T15FC 003:988.302 - 0.019ms returns 0x00000000
T15FC 003:988.317 JLINK_ReadReg(R9)
T15FC 003:988.330 - 0.019ms returns 0x00000000
T15FC 003:988.345 JLINK_ReadReg(R10)
T15FC 003:988.358 - 0.019ms returns 0x00000000
T15FC 003:988.374 JLINK_ReadReg(R11)
T15FC 003:988.386 - 0.019ms returns 0x00000000
T15FC 003:988.402 JLINK_ReadReg(R12)
T15FC 003:988.415 - 0.019ms returns 0x00000000
T15FC 003:988.430 JLINK_ReadReg(R13 (SP))
T15FC 003:988.443 - 0.019ms returns 0x20000588
T15FC 003:988.458 JLINK_ReadReg(R14)
T15FC 003:988.471 - 0.019ms returns 0x080002A5
T15FC 003:988.486 JLINK_ReadReg(R15 (PC))
T15FC 003:988.499 - 0.019ms returns 0x080027C4
T15FC 003:988.514 JLINK_ReadReg(XPSR)
T15FC 003:988.527 - 0.019ms returns 0x61000000
T15FC 003:988.543 JLINK_ReadReg(MSP)
T15FC 003:988.555 - 0.019ms returns 0x20000588
T15FC 003:988.571 JLINK_ReadReg(PSP)
T15FC 003:988.584 - 0.019ms returns 0x00000000
T15FC 003:988.599 JLINK_ReadReg(CFBP)
T15FC 003:988.612 - 0.019ms returns 0x00000000
T15FC 003:988.627 JLINK_ReadReg(FPSCR)
T15FC 003:994.520 - 5.934ms returns 0x00000000
T15FC 003:994.575 JLINK_ReadReg(FPS0)
T15FC 003:994.592 - 0.023ms returns 0x00000000
T15FC 003:994.608 JLINK_ReadReg(FPS1)
T15FC 003:994.621 - 0.019ms returns 0x00000000
T15FC 003:994.638 JLINK_ReadReg(FPS2)
T15FC 003:994.651 - 0.019ms returns 0x00000000
T15FC 003:994.666 JLINK_ReadReg(FPS3)
T15FC 003:994.679 - 0.019ms returns 0x00000000
T15FC 003:994.694 JLINK_ReadReg(FPS4)
T15FC 003:994.707 - 0.019ms returns 0x00000000
T15FC 003:994.722 JLINK_ReadReg(FPS5)
T15FC 003:994.735 - 0.019ms returns 0x00000000
T15FC 003:994.750 JLINK_ReadReg(FPS6)
T15FC 003:994.763 - 0.019ms returns 0x00000000
T15FC 003:994.778 JLINK_ReadReg(FPS7)
T15FC 003:994.791 - 0.022ms returns 0x00000000
T15FC 003:994.821 JLINK_ReadReg(FPS8)
T15FC 003:994.841 - 0.026ms returns 0x00000000
T15FC 003:994.857 JLINK_ReadReg(FPS9)
T15FC 003:994.870 - 0.019ms returns 0x00000000
T15FC 003:994.885 JLINK_ReadReg(FPS10)
T15FC 003:994.898 - 0.019ms returns 0x00000000
T15FC 003:994.913 JLINK_ReadReg(FPS11)
T15FC 003:994.926 - 0.019ms returns 0x00000000
T15FC 003:994.941 JLINK_ReadReg(FPS12)
T15FC 003:994.954 - 0.020ms returns 0x00000000
T15FC 003:994.970 JLINK_ReadReg(FPS13)
T15FC 003:994.983 - 0.019ms returns 0x00000000
T15FC 003:994.998 JLINK_ReadReg(FPS14)
T15FC 003:995.011 - 0.019ms returns 0x00000000
T15FC 003:995.026 JLINK_ReadReg(FPS15)
T15FC 003:995.039 - 0.019ms returns 0x00000000
T15FC 003:995.054 JLINK_ReadReg(FPS16)
T15FC 003:995.067 - 0.019ms returns 0x00000000
T15FC 003:995.082 JLINK_ReadReg(FPS17)
T15FC 003:995.095 - 0.019ms returns 0x00000000
T15FC 003:995.110 JLINK_ReadReg(FPS18)
T15FC 003:995.123 - 0.019ms returns 0x00000000
T15FC 003:995.139 JLINK_ReadReg(FPS19)
T15FC 003:995.151 - 0.019ms returns 0x00000000
T15FC 003:995.167 JLINK_ReadReg(FPS20)
T15FC 003:995.179 - 0.019ms returns 0x00000000
T15FC 003:995.195 JLINK_ReadReg(FPS21)
T15FC 003:995.207 - 0.019ms returns 0x00000000
T15FC 003:995.223 JLINK_ReadReg(FPS22)
T15FC 003:995.235 - 0.019ms returns 0x00000000
T15FC 003:995.251 JLINK_ReadReg(FPS23)
T15FC 003:995.263 - 0.019ms returns 0x00000000
T15FC 003:995.279 JLINK_ReadReg(FPS24)
T15FC 003:995.291 - 0.019ms returns 0x00000000
T15FC 003:995.307 JLINK_ReadReg(FPS25)
T15FC 003:995.319 - 0.019ms returns 0x00000000
T15FC 003:995.335 JLINK_ReadReg(FPS26)
T15FC 003:995.347 - 0.019ms returns 0x00000000
T15FC 003:995.363 JLINK_ReadReg(FPS27)
T15FC 003:995.376 - 0.019ms returns 0x00000000
T15FC 003:995.391 JLINK_ReadReg(FPS28)
T15FC 003:995.404 - 0.019ms returns 0x00000000
T15FC 003:995.419 JLINK_ReadReg(FPS29)
T15FC 003:995.432 - 0.019ms returns 0x00000000
T15FC 003:995.490 JLINK_ReadReg(FPS30)
T15FC 003:995.506 - 0.022ms returns 0x00000000
T15FC 003:995.522 JLINK_ReadReg(FPS31)
T15FC 003:995.534 - 0.019ms returns 0x00000000
T2BFC 003:999.330 JLINK_HasError()
T2BFC 003:999.360 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T2BFC 003:999.383   CPU_ReadMem(4 bytes @ 0xE0001004)
T2BFC 004:000.417   Data:  E6 58 6A 06
T2BFC 004:000.446   Debug reg: DWT_CYCCNT
T2BFC 004:000.464 - 1.110ms returns 1 (0x1)
T15FC 007:182.594 JLINK_ReadMemEx(0x080027C4, 0x2 Bytes, Flags = 0x02000000)
T15FC 007:182.637    -- Merging zombie BP[0]: 0xF7FE @ 0x080027C4
T15FC 007:182.658    -- Merging zombie BP[0]: 0xF7FE @ 0x080027C4
T15FC 007:182.677   Data:  FE F7
T15FC 007:182.697 - 0.109ms returns 2 (0x2)
T15FC 007:182.714 JLINK_HasError()
T15FC 007:182.733 JLINK_SetBPEx(Addr = 0x08002210, Type = 0xFFFFFFF2)
T15FC 007:182.752 - 0.025ms returns 0x0000000D
T15FC 007:182.768 JLINK_HasError()
T15FC 007:182.783 JLINK_SetBPEx(Addr = 0x0800222E, Type = 0xFFFFFFF2)
T15FC 007:182.797 - 0.020ms returns 0x0000000E
T15FC 007:182.813 JLINK_HasError()
T15FC 007:182.829 JLINK_SetBPEx(Addr = 0x08002268, Type = 0xFFFFFFF2)
T15FC 007:182.842 - 0.020ms returns 0x0000000F
T15FC 007:182.858 JLINK_HasError()
T15FC 007:182.874 JLINK_SetBPEx(Addr = 0x0800223E, Type = 0xFFFFFFF2)
T15FC 007:182.887 - 0.020ms returns 0x00000010
T15FC 007:182.903 JLINK_HasError()
T15FC 007:182.919 JLINK_SetBPEx(Addr = 0x08002258, Type = 0xFFFFFFF2)
T15FC 007:182.932 - 0.020ms returns 0x00000011
T15FC 007:182.948 JLINK_HasError()
T15FC 007:182.973 JLINK_SetBPEx(Addr = 0x080027F0, Type = 0xFFFFFFF2)
T15FC 007:183.006 - 0.046ms returns 0x00000012
T15FC 007:183.030 JLINK_HasError()
T15FC 007:183.045 JLINK_SetBPEx(Addr = 0x080027F6, Type = 0xFFFFFFF2)
T15FC 007:183.060 - 0.021ms returns 0x00000013
T15FC 007:183.076 JLINK_HasError()
T15FC 007:183.091 JLINK_SetBPEx(Addr = 0x080027FE, Type = 0xFFFFFFF2)
T15FC 007:183.104 - 0.020ms returns 0x00000014
T15FC 007:183.120 JLINK_HasError()
T15FC 007:183.135 JLINK_SetBPEx(Addr = 0x0800280C, Type = 0xFFFFFFF2)
T15FC 007:183.148 - 0.019ms returns 0x00000015
T15FC 007:183.163 JLINK_HasError()
T15FC 007:183.178 JLINK_SetBPEx(Addr = 0x08002814, Type = 0xFFFFFFF2)
T15FC 007:183.192 - 0.019ms returns 0x00000016
T15FC 007:183.207 JLINK_HasError()
T15FC 007:183.222 JLINK_SetBPEx(Addr = 0x08002222, Type = 0xFFFFFFF2)
T15FC 007:183.235 - 0.021ms returns 0x00000017
T15FC 007:183.253 JLINK_HasError()
T15FC 007:183.268 JLINK_HasError()
T15FC 007:183.283 JLINK_Go()
T15FC 007:183.942    -- Merging zombie BP[0]: 0xF7FE @ 0x080027C4
T15FC 007:183.972    -- Merging zombie BP[0]: 0xF7FE @ 0x080027C4
T15FC 007:183.995    -- Read from flash cache (2 bytes @ 0x080027C6)
T15FC 007:184.030   -- Simulated
T15FC 007:184.050    -- Read from flash cache (2 bytes @ 0x0800108C)
T15FC 007:184.069    -- Read from flash cache (4 bytes @ 0x080010BC)
T15FC 007:184.088   -- Simulated
T15FC 007:184.107    -- Read from flash cache (2 bytes @ 0x0800108E)
T15FC 007:184.133   CPU_WriteMem(8 bytes @ 0x20000580)
T15FC 007:184.851   -- Simulated
T15FC 007:184.890    -- Read from flash cache (2 bytes @ 0x08001090)
T15FC 007:184.912   -- Not simulated
T15FC 007:184.941   CPU_ReadMem(4 bytes @ 0xE000ED90)
T15FC 007:185.616   CPU_ReadMem(4 bytes @ 0xE000ED94)
T15FC 007:186.287    -- Start of preparing flash programming
T15FC 007:186.327    -- Calculating RAM usage
T15FC 007:186.362    -- RAM usage = 10012 Bytes
T15FC 007:186.390    -- Preserving CPU registers
T15FC 007:186.436    -- Preparing memory
T15FC 007:186.463    -- Preparing target
T15FC 007:194.551    -- Preserving target RAM temporarily used for programming
T15FC 007:296.878    -- Downloading RAMCode
T15FC 007:328.417    -- Preparing RAMCode
T15FC 007:336.007    -- Not first flash download, checking target RAM skipped
T15FC 007:336.044    -- Preparing RAMCode
T15FC 007:338.218    -- End of preparing flash programming
T15FC 007:338.274    -- Read from flash cache (16384 bytes @ 0x08000000)
T15FC 007:338.298    -- Programming range 0x08000000 - 0x08003FFF (  1 Sector, 16 KB)
T15FC 007:854.068    -- Start of restoring
T15FC 007:854.111    -- Restoring RAMCode
T15FC 007:860.279    -- Restoring target memory
T15FC 007:972.864    -- Restore target
T15FC 007:976.595    -- Restore memory
T15FC 007:976.638    -- Restoring CPU registers
T15FC 007:976.672    -- End of restoring
T15FC 007:977.878   CPU_WriteMem(4 bytes @ 0xE0002000)
T15FC 007:978.648   CPU_ReadMem(4 bytes @ 0xE0002008)
T15FC 007:979.326   CPU_WriteMem(4 bytes @ 0xE000EDF4)
T15FC 007:980.013   CPU_ReadMem(4 bytes @ 0xE000EDF8)
T15FC 007:980.973   CPU_WriteMem(4 bytes @ 0xE0002008)
T15FC 007:981.950   CPU_WriteMem(4 bytes @ 0xE000EDF8)
T15FC 007:982.672   CPU_WriteMem(4 bytes @ 0xE000EDF4)
T15FC 007:983.535   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T15FC 007:984.664   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T15FC 007:985.658   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T15FC 007:986.602   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T15FC 007:987.637   CPU_WriteMem(4 bytes @ 0xE0002008)
T15FC 007:988.742   CPU_WriteMem(4 bytes @ 0xE000EDF8)
T15FC 007:989.788   CPU_WriteMem(4 bytes @ 0xE000EDF4)
T15FC 007:990.704   CPU_WriteMem(4 bytes @ 0xE0002000)
T15FC 007:991.740   CPU_WriteMem(4 bytes @ 0xE0002000)
T15FC 007:992.509   CPU_WriteMem(4 bytes @ 0xE0002000)
T15FC 007:993.360   CPU_WriteMem(4 bytes @ 0xE0002000)
T15FC 007:994.433   CPU_WriteMem(4 bytes @ 0xE0002000)
T15FC 007:995.546   CPU_WriteMem(4 bytes @ 0xE0002000)
T15FC 007:996.623   CPU_ReadMem(4 bytes @ 0xE0001000)
T15FC 007:997.741   CPU_WriteMem(4 bytes @ 0xE0002008)
T15FC 007:997.770   CPU_WriteMem(4 bytes @ 0xE000200C)
T15FC 007:997.790   CPU_WriteMem(4 bytes @ 0xE0002010)
T15FC 007:997.808   CPU_WriteMem(4 bytes @ 0xE0002014)
T15FC 007:997.827   CPU_WriteMem(4 bytes @ 0xE0002018)
T15FC 007:997.845   CPU_WriteMem(4 bytes @ 0xE000201C)
T15FC 008:004.776 - 821.507ms
T15FC 008:104.894 JLINK_HasError()
T15FC 008:104.937 JLINK_IsHalted()
T15FC 008:108.964 - 4.068ms returns TRUE
T15FC 008:109.042 JLINK_HasError()
T15FC 008:109.069 JLINK_Halt()
T15FC 008:109.090 - 0.037ms returns 0x00
T15FC 008:109.140 JLINK_IsHalted()
T15FC 008:109.187 - 0.068ms returns TRUE
T15FC 008:109.246 JLINK_IsHalted()
T15FC 008:109.296 - 0.080ms returns TRUE
T15FC 008:109.345 JLINK_IsHalted()
T15FC 008:109.371 - 0.032ms returns TRUE
T15FC 008:109.394 JLINK_HasError()
T15FC 008:109.427 JLINK_ReadReg(R15 (PC))
T15FC 008:109.454 - 0.033ms returns 0x080027F0
T15FC 008:109.477 JLINK_ReadReg(XPSR)
T15FC 008:109.502 - 0.032ms returns 0x81000000
T15FC 008:109.575 JLINK_HasError()
T15FC 008:109.599 JLINK_ClrBPEx(BPHandle = 0x0000000D)
T15FC 008:109.614 - 0.021ms returns 0x00
T15FC 008:109.630 JLINK_HasError()
T15FC 008:109.645 JLINK_ClrBPEx(BPHandle = 0x0000000E)
T15FC 008:109.658 - 0.019ms returns 0x00
T15FC 008:109.673 JLINK_HasError()
T15FC 008:109.688 JLINK_ClrBPEx(BPHandle = 0x0000000F)
T15FC 008:109.701 - 0.019ms returns 0x00
T15FC 008:109.717 JLINK_HasError()
T15FC 008:109.739 JLINK_ClrBPEx(BPHandle = 0x00000010)
T15FC 008:109.768 - 0.043ms returns 0x00
T15FC 008:109.800 JLINK_HasError()
T15FC 008:109.818 JLINK_ClrBPEx(BPHandle = 0x00000011)
T15FC 008:109.832 - 0.020ms returns 0x00
T15FC 008:109.847 JLINK_HasError()
T15FC 008:109.862 JLINK_ClrBPEx(BPHandle = 0x00000012)
T15FC 008:109.876 - 0.019ms returns 0x00
T15FC 008:109.891 JLINK_HasError()
T15FC 008:109.907 JLINK_ClrBPEx(BPHandle = 0x00000013)
T15FC 008:109.920 - 0.019ms returns 0x00
T15FC 008:109.935 JLINK_HasError()
T15FC 008:109.950 JLINK_ClrBPEx(BPHandle = 0x00000014)
T15FC 008:109.963 - 0.019ms returns 0x00
T15FC 008:109.978 JLINK_HasError()
T15FC 008:109.993 JLINK_ClrBPEx(BPHandle = 0x00000015)
T15FC 008:110.006 - 0.019ms returns 0x00
T15FC 008:110.021 JLINK_HasError()
T15FC 008:110.036 JLINK_ClrBPEx(BPHandle = 0x00000016)
T15FC 008:110.049 - 0.019ms returns 0x00
T15FC 008:110.064 JLINK_HasError()
T15FC 008:110.079 JLINK_ClrBPEx(BPHandle = 0x00000017)
T15FC 008:110.092 - 0.019ms returns 0x00
T15FC 008:110.107 JLINK_HasError()
T15FC 008:110.126 JLINK_HasError()
T15FC 008:110.161 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T15FC 008:110.202   CPU_ReadMem(4 bytes @ 0xE000ED30)
T15FC 008:110.910   Data:  03 00 00 00
T15FC 008:110.940 - 0.786ms returns 1 (0x1)
T15FC 008:110.960 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T15FC 008:110.979   CPU_ReadMem(4 bytes @ 0xE0001028)
T15FC 008:111.793   Data:  00 00 00 00
T15FC 008:111.829   Debug reg: DWT_FUNC[0]
T15FC 008:111.853 - 0.899ms returns 1 (0x1)
T15FC 008:111.871 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T15FC 008:111.891   CPU_ReadMem(4 bytes @ 0xE0001038)
T15FC 008:112.818   Data:  00 02 00 00
T15FC 008:112.852   Debug reg: DWT_FUNC[1]
T15FC 008:112.872 - 1.007ms returns 1 (0x1)
T15FC 008:112.891 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T15FC 008:112.910   CPU_ReadMem(4 bytes @ 0xE0001048)
T15FC 008:113.807   Data:  00 00 00 00
T15FC 008:113.835   Debug reg: DWT_FUNC[2]
T15FC 008:113.854 - 0.969ms returns 1 (0x1)
T15FC 008:113.873 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T15FC 008:113.892   CPU_ReadMem(4 bytes @ 0xE0001058)
T15FC 008:114.865   Data:  00 00 00 00
T15FC 008:114.901   Debug reg: DWT_FUNC[3]
T15FC 008:114.920 - 1.053ms returns 1 (0x1)
T15FC 008:114.973 JLINK_HasError()
T15FC 008:114.991 JLINK_ReadReg(R0)
T15FC 008:115.008 - 0.023ms returns 0x40005400
T15FC 008:115.024 JLINK_ReadReg(R1)
T15FC 008:115.037 - 0.019ms returns 0x00000001
T15FC 008:115.053 JLINK_ReadReg(R2)
T15FC 008:115.066 - 0.019ms returns 0x00000101
T15FC 008:115.081 JLINK_ReadReg(R3)
T15FC 008:115.094 - 0.019ms returns 0x00000000
T15FC 008:115.109 JLINK_ReadReg(R4)
T15FC 008:115.122 - 0.019ms returns 0x0800285C
T15FC 008:115.137 JLINK_ReadReg(R5)
T15FC 008:115.150 - 0.019ms returns 0x0800285C
T15FC 008:115.165 JLINK_ReadReg(R6)
T15FC 008:115.178 - 0.019ms returns 0x00000000
T15FC 008:115.193 JLINK_ReadReg(R7)
T15FC 008:115.206 - 0.019ms returns 0x00000000
T15FC 008:115.221 JLINK_ReadReg(R8)
T15FC 008:115.234 - 0.019ms returns 0x00000000
T15FC 008:115.249 JLINK_ReadReg(R9)
T15FC 008:115.262 - 0.019ms returns 0x00000000
T15FC 008:115.278 JLINK_ReadReg(R10)
T15FC 008:115.290 - 0.019ms returns 0x00000000
T15FC 008:115.306 JLINK_ReadReg(R11)
T15FC 008:115.318 - 0.019ms returns 0x00000000
T15FC 008:115.334 JLINK_ReadReg(R12)
T15FC 008:115.347 - 0.019ms returns 0x0000000E
T15FC 008:115.362 JLINK_ReadReg(R13 (SP))
T15FC 008:115.375 - 0.019ms returns 0x20000588
T15FC 008:115.390 JLINK_ReadReg(R14)
T15FC 008:115.403 - 0.019ms returns 0x080027F1
T15FC 008:115.418 JLINK_ReadReg(R15 (PC))
T15FC 008:115.431 - 0.019ms returns 0x080027F0
T15FC 008:115.448 JLINK_ReadReg(XPSR)
T15FC 008:115.463 - 0.021ms returns 0x81000000
T15FC 008:115.479 JLINK_ReadReg(MSP)
T15FC 008:115.491 - 0.019ms returns 0x20000588
T15FC 008:115.507 JLINK_ReadReg(PSP)
T15FC 008:115.520 - 0.019ms returns 0x00000000
T15FC 008:115.535 JLINK_ReadReg(CFBP)
T15FC 008:115.548 - 0.019ms returns 0x00000000
T15FC 008:115.563 JLINK_ReadReg(FPSCR)
T15FC 008:121.454 - 5.905ms returns 0x00000000
T15FC 008:121.483 JLINK_ReadReg(FPS0)
T15FC 008:121.498 - 0.022ms returns 0x00000000
T15FC 008:121.514 JLINK_ReadReg(FPS1)
T15FC 008:121.527 - 0.019ms returns 0x00000000
T15FC 008:121.542 JLINK_ReadReg(FPS2)
T15FC 008:121.555 - 0.019ms returns 0x00000000
T15FC 008:121.571 JLINK_ReadReg(FPS3)
T15FC 008:121.583 - 0.019ms returns 0x00000000
T15FC 008:121.599 JLINK_ReadReg(FPS4)
T15FC 008:121.611 - 0.019ms returns 0x00000000
T15FC 008:121.626 JLINK_ReadReg(FPS5)
T15FC 008:121.639 - 0.019ms returns 0x00000000
T15FC 008:121.654 JLINK_ReadReg(FPS6)
T15FC 008:121.667 - 0.019ms returns 0x00000000
T15FC 008:121.682 JLINK_ReadReg(FPS7)
T15FC 008:121.695 - 0.019ms returns 0x00000000
T15FC 008:121.710 JLINK_ReadReg(FPS8)
T15FC 008:121.723 - 0.019ms returns 0x00000000
T15FC 008:121.738 JLINK_ReadReg(FPS9)
T15FC 008:121.751 - 0.019ms returns 0x00000000
T15FC 008:121.769 JLINK_ReadReg(FPS10)
T15FC 008:121.796 - 0.039ms returns 0x00000000
T15FC 008:121.820 JLINK_ReadReg(FPS11)
T15FC 008:121.834 - 0.021ms returns 0x00000000
T15FC 008:121.850 JLINK_ReadReg(FPS12)
T15FC 008:121.867 - 0.024ms returns 0x00000000
T15FC 008:121.883 JLINK_ReadReg(FPS13)
T15FC 008:121.897 - 0.021ms returns 0x00000000
T15FC 008:121.913 JLINK_ReadReg(FPS14)
T15FC 008:121.926 - 0.019ms returns 0x00000000
T15FC 008:121.941 JLINK_ReadReg(FPS15)
T15FC 008:121.954 - 0.019ms returns 0x00000000
T15FC 008:121.969 JLINK_ReadReg(FPS16)
T15FC 008:121.982 - 0.019ms returns 0x00000000
T15FC 008:121.997 JLINK_ReadReg(FPS17)
T15FC 008:122.010 - 0.019ms returns 0x00000000
T15FC 008:122.025 JLINK_ReadReg(FPS18)
T15FC 008:122.038 - 0.019ms returns 0x00000000
T15FC 008:122.053 JLINK_ReadReg(FPS19)
T15FC 008:122.066 - 0.019ms returns 0x00000000
T15FC 008:122.082 JLINK_ReadReg(FPS20)
T15FC 008:122.094 - 0.019ms returns 0x00000000
T15FC 008:122.109 JLINK_ReadReg(FPS21)
T15FC 008:122.122 - 0.019ms returns 0x00000000
T15FC 008:122.137 JLINK_ReadReg(FPS22)
T15FC 008:122.152 - 0.020ms returns 0x00000000
T15FC 008:122.167 JLINK_ReadReg(FPS23)
T15FC 008:122.180 - 0.019ms returns 0x00000000
T15FC 008:122.195 JLINK_ReadReg(FPS24)
T15FC 008:122.208 - 0.019ms returns 0x00000000
T15FC 008:122.223 JLINK_ReadReg(FPS25)
T15FC 008:122.236 - 0.019ms returns 0x00000000
T15FC 008:122.251 JLINK_ReadReg(FPS26)
T15FC 008:122.264 - 0.019ms returns 0x00000000
T15FC 008:122.280 JLINK_ReadReg(FPS27)
T15FC 008:122.293 - 0.019ms returns 0x00000000
T15FC 008:122.308 JLINK_ReadReg(FPS28)
T15FC 008:122.321 - 0.019ms returns 0x00000000
T15FC 008:122.337 JLINK_ReadReg(FPS29)
T15FC 008:122.349 - 0.019ms returns 0x00000000
T15FC 008:122.365 JLINK_ReadReg(FPS30)
T15FC 008:122.378 - 0.019ms returns 0x00000000
T15FC 008:122.393 JLINK_ReadReg(FPS31)
T15FC 008:122.405 - 0.019ms returns 0x00000000
T2BFC 008:122.612 JLINK_HasError()
T2BFC 008:122.665 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T2BFC 008:122.687   CPU_ReadMem(4 bytes @ 0xE0001004)
T2BFC 008:123.410   Data:  50 E2 CB 09
T2BFC 008:123.438   Debug reg: DWT_CYCCNT
T2BFC 008:123.457 - 0.799ms returns 1 (0x1)
T2BFC 008:133.356 JLINK_ReadMemEx(0x080027F0, 0x3C Bytes, Flags = 0x02000000)
T2BFC 008:133.394    -- Read from flash cache (60 bytes @ 0x080027F0)
T2BFC 008:133.415   Data:  09 4C 20 78 18 B1 FF F7 09 FD 00 20 20 70 08 21 ...
T2BFC 008:133.433 - 0.083ms returns 60 (0x3C)
T2BFC 008:133.452 JLINK_ReadMemEx(0x080027F0, 0x2 Bytes, Flags = 0x02000000)
T2BFC 008:133.466    -- Read from flash cache (2 bytes @ 0x080027F0)
T2BFC 008:133.484   Data:  09 4C
T2BFC 008:133.502 - 0.056ms returns 2 (0x2)
T2BFC 008:133.518 JLINK_ReadMemEx(0x080027F2, 0x2 Bytes, Flags = 0x02000000)
T2BFC 008:133.531    -- Read from flash cache (2 bytes @ 0x080027F2)
T2BFC 008:133.549   Data:  20 78
T2BFC 008:133.567 - 0.055ms returns 2 (0x2)
T2BFC 008:133.588 JLINK_ReadMemEx(0x080027F2, 0x2 Bytes, Flags = 0x02000000)
T2BFC 008:133.602    -- Read from flash cache (2 bytes @ 0x080027F2)
T2BFC 008:133.620   Data:  20 78
T2BFC 008:133.638 - 0.055ms returns 2 (0x2)
T2BFC 008:133.653 JLINK_ReadMemEx(0x080027F4, 0x3C Bytes, Flags = 0x02000000)
T2BFC 008:133.666    -- Read from flash cache (60 bytes @ 0x080027F4)
T2BFC 008:133.685   Data:  18 B1 FF F7 09 FD 00 20 20 70 08 21 00 20 FF F7 ...
T2BFC 008:133.715 - 0.069ms returns 60 (0x3C)
T2BFC 008:133.733 JLINK_ReadMemEx(0x080027F4, 0x2 Bytes, Flags = 0x02000000)
T2BFC 008:133.747    -- Read from flash cache (2 bytes @ 0x080027F4)
T2BFC 008:133.765   Data:  18 B1
T2BFC 008:133.783 - 0.056ms returns 2 (0x2)
T2BFC 008:133.804 JLINK_ReadMemEx(0x080027F4, 0x3C Bytes, Flags = 0x02000000)
T2BFC 008:133.817    -- Read from flash cache (60 bytes @ 0x080027F4)
T2BFC 008:133.836   Data:  18 B1 FF F7 09 FD 00 20 20 70 08 21 00 20 FF F7 ...
T2BFC 008:133.854 - 0.057ms returns 60 (0x3C)
T2BFC 008:133.870 JLINK_ReadMemEx(0x080027F4, 0x2 Bytes, Flags = 0x02000000)
T2BFC 008:133.883    -- Read from flash cache (2 bytes @ 0x080027F4)
T2BFC 008:133.901   Data:  18 B1
T2BFC 008:133.919 - 0.055ms returns 2 (0x2)
T2BFC 008:133.934 JLINK_ReadMemEx(0x080027F6, 0x2 Bytes, Flags = 0x02000000)
T2BFC 008:133.947    -- Read from flash cache (2 bytes @ 0x080027F6)
T2BFC 008:133.971   Data:  FF F7
T2BFC 008:133.990 - 0.061ms returns 2 (0x2)
T15FC 009:055.113 JLINK_ReadMemEx(0x080027F0, 0x2 Bytes, Flags = 0x02000000)
T15FC 009:055.153    -- Read from flash cache (2 bytes @ 0x080027F0)
T15FC 009:055.173   Data:  09 4C
T15FC 009:055.193 - 0.085ms returns 2 (0x2)
T15FC 009:055.210 JLINK_HasError()
T15FC 009:055.228 JLINK_Step()
T15FC 009:055.945    -- Read from flash cache (2 bytes @ 0x080027F0)
T15FC 009:055.982    -- Read from flash cache (4 bytes @ 0x08002818)
T15FC 009:056.003   -- Simulated
T15FC 009:056.022 - 0.800ms returns 0
T15FC 009:056.042 JLINK_HasError()
T15FC 009:056.059 JLINK_ReadReg(R15 (PC))
T15FC 009:056.074 - 0.021ms returns 0x080027F2
T15FC 009:056.090 JLINK_ReadReg(XPSR)
T15FC 009:056.104 - 0.020ms returns 0x81000000
T15FC 009:056.128 JLINK_HasError()
T15FC 009:056.145 JLINK_Step()
T15FC 009:056.159    -- Read from flash cache (2 bytes @ 0x080027F2)
T15FC 009:056.183   CPU_ReadMem(64 bytes @ 0x20000000)
T15FC 009:057.591    -- Updating C cache (64 bytes @ 0x20000000)
T15FC 009:057.626    -- Read from C cache (1 bytes @ 0x2000000C)
T15FC 009:057.648   -- Simulated
T15FC 009:057.667 - 1.529ms returns 0
T15FC 009:057.687 JLINK_HasError()
T15FC 009:057.704 JLINK_ReadReg(R15 (PC))
T15FC 009:057.720 - 0.022ms returns 0x080027F4
T15FC 009:057.736 JLINK_ReadReg(XPSR)
T15FC 009:057.750 - 0.020ms returns 0x81000000
T15FC 009:057.775 JLINK_HasError()
T15FC 009:057.791 JLINK_Step()
T15FC 009:057.806    -- Read from flash cache (2 bytes @ 0x080027F4)
T15FC 009:057.826   -- Simulated
T15FC 009:057.845 - 0.060ms returns 0
T15FC 009:057.860 JLINK_HasError()
T15FC 009:057.875 JLINK_ReadReg(R15 (PC))
T15FC 009:057.889 - 0.020ms returns 0x080027F6
T15FC 009:057.905 JLINK_ReadReg(XPSR)
T15FC 009:057.918 - 0.019ms returns 0x81000000
T15FC 009:058.706 JLINK_HasError()
T15FC 009:058.741 JLINK_ReadReg(R0)
T15FC 009:058.765 - 0.032ms returns 0x00000001
T15FC 009:058.788 JLINK_ReadReg(R1)
T15FC 009:058.807 - 0.027ms returns 0x00000001
T15FC 009:058.828 JLINK_ReadReg(R2)
T15FC 009:058.852 - 0.035ms returns 0x00000101
T15FC 009:058.878 JLINK_ReadReg(R3)
T15FC 009:058.898 - 0.030ms returns 0x00000000
T15FC 009:058.924 JLINK_ReadReg(R4)
T15FC 009:058.945 - 0.030ms returns 0x2000000C
T15FC 009:058.967 JLINK_ReadReg(R5)
T15FC 009:058.986 - 0.028ms returns 0x0800285C
T15FC 009:059.013 JLINK_ReadReg(R6)
T15FC 009:059.032 - 0.025ms returns 0x00000000
T15FC 009:059.048 JLINK_ReadReg(R7)
T15FC 009:059.061 - 0.019ms returns 0x00000000
T15FC 009:059.076 JLINK_ReadReg(R8)
T15FC 009:059.089 - 0.019ms returns 0x00000000
T15FC 009:059.105 JLINK_ReadReg(R9)
T15FC 009:059.118 - 0.019ms returns 0x00000000
T15FC 009:059.133 JLINK_ReadReg(R10)
T15FC 009:059.148 - 0.024ms returns 0x00000000
T15FC 009:059.167 JLINK_ReadReg(R11)
T15FC 009:059.184 - 0.023ms returns 0x00000000
T15FC 009:059.275 JLINK_ReadReg(R12)
T15FC 009:059.290 - 0.022ms returns 0x0000000E
T15FC 009:059.306 JLINK_ReadReg(R13 (SP))
T15FC 009:059.319 - 0.019ms returns 0x20000588
T15FC 009:059.335 JLINK_ReadReg(R14)
T15FC 009:059.347 - 0.019ms returns 0x080027F1
T15FC 009:059.363 JLINK_ReadReg(R15 (PC))
T15FC 009:059.376 - 0.019ms returns 0x080027F6
T15FC 009:059.391 JLINK_ReadReg(XPSR)
T15FC 009:059.404 - 0.019ms returns 0x81000000
T15FC 009:059.419 JLINK_ReadReg(MSP)
T15FC 009:059.431 - 0.019ms returns 0x20000588
T15FC 009:059.447 JLINK_ReadReg(PSP)
T15FC 009:059.459 - 0.019ms returns 0x00000000
T15FC 009:059.475 JLINK_ReadReg(CFBP)
T15FC 009:059.487 - 0.018ms returns 0x00000000
T15FC 009:059.503 JLINK_ReadReg(FPSCR)
T15FC 009:059.516 - 0.019ms returns 0x00000000
T15FC 009:059.531 JLINK_ReadReg(FPS0)
T15FC 009:059.543 - 0.018ms returns 0x00000000
T15FC 009:059.559 JLINK_ReadReg(FPS1)
T15FC 009:059.571 - 0.018ms returns 0x00000000
T15FC 009:059.586 JLINK_ReadReg(FPS2)
T15FC 009:059.599 - 0.018ms returns 0x00000000
T15FC 009:059.614 JLINK_ReadReg(FPS3)
T15FC 009:059.627 - 0.019ms returns 0x00000000
T15FC 009:059.642 JLINK_ReadReg(FPS4)
T15FC 009:059.658 - 0.024ms returns 0x00000000
T15FC 009:059.675 JLINK_ReadReg(FPS5)
T15FC 009:059.688 - 0.018ms returns 0x00000000
T15FC 009:059.703 JLINK_ReadReg(FPS6)
T15FC 009:059.716 - 0.019ms returns 0x00000000
T15FC 009:059.731 JLINK_ReadReg(FPS7)
T15FC 009:059.744 - 0.018ms returns 0x00000000
T15FC 009:059.759 JLINK_ReadReg(FPS8)
T15FC 009:059.771 - 0.018ms returns 0x00000000
T15FC 009:059.787 JLINK_ReadReg(FPS9)
T15FC 009:059.799 - 0.019ms returns 0x00000000
T15FC 009:059.815 JLINK_ReadReg(FPS10)
T15FC 009:059.827 - 0.019ms returns 0x00000000
T15FC 009:059.842 JLINK_ReadReg(FPS11)
T15FC 009:059.855 - 0.018ms returns 0x00000000
T15FC 009:059.870 JLINK_ReadReg(FPS12)
T15FC 009:059.883 - 0.018ms returns 0x00000000
T15FC 009:059.898 JLINK_ReadReg(FPS13)
T15FC 009:059.911 - 0.019ms returns 0x00000000
T15FC 009:059.926 JLINK_ReadReg(FPS14)
T15FC 009:059.939 - 0.018ms returns 0x00000000
T15FC 009:059.954 JLINK_ReadReg(FPS15)
T15FC 009:059.966 - 0.018ms returns 0x00000000
T15FC 009:059.981 JLINK_ReadReg(FPS16)
T15FC 009:059.994 - 0.018ms returns 0x00000000
T15FC 009:060.009 JLINK_ReadReg(FPS17)
T15FC 009:060.022 - 0.018ms returns 0x00000000
T15FC 009:060.037 JLINK_ReadReg(FPS18)
T15FC 009:060.050 - 0.018ms returns 0x00000000
T15FC 009:060.065 JLINK_ReadReg(FPS19)
T15FC 009:060.077 - 0.018ms returns 0x00000000
T15FC 009:060.093 JLINK_ReadReg(FPS20)
T15FC 009:060.105 - 0.019ms returns 0x00000000
T15FC 009:060.120 JLINK_ReadReg(FPS21)
T15FC 009:060.133 - 0.019ms returns 0x00000000
T15FC 009:060.148 JLINK_ReadReg(FPS22)
T15FC 009:060.161 - 0.018ms returns 0x00000000
T15FC 009:060.176 JLINK_ReadReg(FPS23)
T15FC 009:060.189 - 0.019ms returns 0x00000000
T15FC 009:060.204 JLINK_ReadReg(FPS24)
T15FC 009:060.218 - 0.025ms returns 0x00000000
T15FC 009:060.242 JLINK_ReadReg(FPS25)
T15FC 009:060.265 - 0.036ms returns 0x00000000
T15FC 009:060.290 JLINK_ReadReg(FPS26)
T15FC 009:060.304 - 0.020ms returns 0x00000000
T15FC 009:060.319 JLINK_ReadReg(FPS27)
T15FC 009:060.332 - 0.019ms returns 0x00000000
T15FC 009:060.347 JLINK_ReadReg(FPS28)
T15FC 009:060.360 - 0.019ms returns 0x00000000
T15FC 009:060.375 JLINK_ReadReg(FPS29)
T15FC 009:060.388 - 0.018ms returns 0x00000000
T15FC 009:060.403 JLINK_ReadReg(FPS30)
T15FC 009:060.416 - 0.019ms returns 0x00000000
T15FC 009:060.431 JLINK_ReadReg(FPS31)
T15FC 009:060.444 - 0.018ms returns 0x00000000
T2BFC 009:060.645 JLINK_HasError()
T2BFC 009:060.673 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T2BFC 009:060.697   Data:  55 E2 CB 09
T2BFC 009:060.716   Debug reg: DWT_CYCCNT
T2BFC 009:060.735 - 0.067ms returns 1 (0x1)
T2BFC 009:070.951 JLINK_ReadMemEx(0x080027F6, 0x2 Bytes, Flags = 0x02000000)
T2BFC 009:070.994    -- Read from flash cache (2 bytes @ 0x080027F6)
T2BFC 009:071.013   Data:  FF F7
T2BFC 009:071.032 - 0.086ms returns 2 (0x2)
T2BFC 009:071.049 JLINK_ReadMemEx(0x080027F8, 0x3C Bytes, Flags = 0x02000000)
T2BFC 009:071.062    -- Read from flash cache (60 bytes @ 0x080027F8)
T2BFC 009:071.081   Data:  09 FD 00 20 20 70 08 21 00 20 FF F7 13 FB 05 48 ...
T2BFC 009:071.099 - 0.056ms returns 60 (0x3C)
T2BFC 009:071.114 JLINK_ReadMemEx(0x080027F8, 0x2 Bytes, Flags = 0x02000000)
T2BFC 009:071.127    -- Read from flash cache (2 bytes @ 0x080027F8)
T2BFC 009:071.146   Data:  09 FD
T2BFC 009:071.164 - 0.056ms returns 2 (0x2)
T15FC 010:556.490 JLINK_ReadMemEx(0x080027F6, 0x2 Bytes, Flags = 0x02000000)
T15FC 010:556.535    -- Read from flash cache (2 bytes @ 0x080027F6)
T15FC 010:556.567   Data:  FF F7
T15FC 010:556.602 - 0.125ms returns 2 (0x2)
T15FC 010:556.637 JLINK_HasError()
T15FC 010:556.667 JLINK_Step()
T15FC 010:557.331    -- Read from flash cache (2 bytes @ 0x080027F6)
T15FC 010:557.405    -- Read from flash cache (2 bytes @ 0x080027F8)
T15FC 010:557.449   -- Simulated
T15FC 010:557.495 - 0.843ms returns 0
T15FC 010:557.536 JLINK_HasError()
T15FC 010:557.561 JLINK_ReadReg(R15 (PC))
T15FC 010:557.609 - 0.066ms returns 0x0800220C
T15FC 010:557.648 JLINK_ReadReg(XPSR)
T15FC 010:557.668 - 0.029ms returns 0x81000000
T15FC 010:558.857 JLINK_HasError()
T15FC 010:558.911 JLINK_ReadReg(R0)
T15FC 010:558.939 - 0.038ms returns 0x00000001
T15FC 010:558.964 JLINK_ReadReg(R1)
T15FC 010:558.985 - 0.032ms returns 0x00000001
T15FC 010:559.011 JLINK_ReadReg(R2)
T15FC 010:559.032 - 0.030ms returns 0x00000101
T15FC 010:559.057 JLINK_ReadReg(R3)
T15FC 010:559.079 - 0.032ms returns 0x00000000
T15FC 010:559.102 JLINK_ReadReg(R4)
T15FC 010:559.123 - 0.030ms returns 0x2000000C
T15FC 010:559.146 JLINK_ReadReg(R5)
T15FC 010:559.171 - 0.034ms returns 0x0800285C
T15FC 010:559.196 JLINK_ReadReg(R6)
T15FC 010:559.229 - 0.048ms returns 0x00000000
T15FC 010:559.266 JLINK_ReadReg(R7)
T15FC 010:559.296 - 0.039ms returns 0x00000000
T15FC 010:559.318 JLINK_ReadReg(R8)
T15FC 010:559.337 - 0.032ms returns 0x00000000
T15FC 010:559.367 JLINK_ReadReg(R9)
T15FC 010:559.389 - 0.035ms returns 0x00000000
T15FC 010:559.428 JLINK_ReadReg(R10)
T15FC 010:559.502 - 0.097ms returns 0x00000000
T15FC 010:559.552 JLINK_ReadReg(R11)
T15FC 010:559.606 - 0.083ms returns 0x00000000
T15FC 010:559.666 JLINK_ReadReg(R12)
T15FC 010:559.717 - 0.075ms returns 0x0000000E
T15FC 010:559.774 JLINK_ReadReg(R13 (SP))
T15FC 010:559.829 - 0.083ms returns 0x20000588
T15FC 010:559.913 JLINK_ReadReg(R14)
T15FC 010:559.977 - 0.092ms returns 0x080027FB
T15FC 010:560.043 JLINK_ReadReg(R15 (PC))
T15FC 010:560.114 - 0.098ms returns 0x0800220C
T15FC 010:560.179 JLINK_ReadReg(XPSR)
T15FC 010:560.240 - 0.110ms returns 0x81000000
T15FC 010:560.331 JLINK_ReadReg(MSP)
T15FC 010:560.399 - 0.103ms returns 0x20000588
T15FC 010:560.470 JLINK_ReadReg(PSP)
T15FC 010:560.533 - 0.086ms returns 0x00000000
T15FC 010:560.574 JLINK_ReadReg(CFBP)
T15FC 010:560.593 - 0.029ms returns 0x00000000
T15FC 010:560.617 JLINK_ReadReg(FPSCR)
T15FC 010:560.638 - 0.031ms returns 0x00000000
T15FC 010:560.662 JLINK_ReadReg(FPS0)
T15FC 010:560.690 - 0.040ms returns 0x00000000
T15FC 010:560.722 JLINK_ReadReg(FPS1)
T15FC 010:560.761 - 0.058ms returns 0x00000000
T15FC 010:560.795 JLINK_ReadReg(FPS2)
T15FC 010:560.817 - 0.031ms returns 0x00000000
T15FC 010:560.840 JLINK_ReadReg(FPS3)
T15FC 010:560.861 - 0.031ms returns 0x00000000
T15FC 010:560.885 JLINK_ReadReg(FPS4)
T15FC 010:560.908 - 0.033ms returns 0x00000000
T15FC 010:560.932 JLINK_ReadReg(FPS5)
T15FC 010:560.953 - 0.030ms returns 0x00000000
T15FC 010:560.977 JLINK_ReadReg(FPS6)
T15FC 010:560.998 - 0.031ms returns 0x00000000
T15FC 010:561.022 JLINK_ReadReg(FPS7)
T15FC 010:561.042 - 0.029ms returns 0x00000000
T15FC 010:561.066 JLINK_ReadReg(FPS8)
T15FC 010:561.087 - 0.031ms returns 0x00000000
T15FC 010:561.112 JLINK_ReadReg(FPS9)
T15FC 010:561.133 - 0.030ms returns 0x00000000
T15FC 010:561.157 JLINK_ReadReg(FPS10)
T15FC 010:561.177 - 0.030ms returns 0x00000000
T15FC 010:561.201 JLINK_ReadReg(FPS11)
T15FC 010:561.224 - 0.032ms returns 0x00000000
T15FC 010:561.249 JLINK_ReadReg(FPS12)
T15FC 010:561.269 - 0.030ms returns 0x00000000
T15FC 010:561.293 JLINK_ReadReg(FPS13)
T15FC 010:561.314 - 0.030ms returns 0x00000000
T15FC 010:561.337 JLINK_ReadReg(FPS14)
T15FC 010:561.358 - 0.030ms returns 0x00000000
T15FC 010:561.380 JLINK_ReadReg(FPS15)
T15FC 010:561.401 - 0.030ms returns 0x00000000
T15FC 010:561.425 JLINK_ReadReg(FPS16)
T15FC 010:561.446 - 0.030ms returns 0x00000000
T15FC 010:561.469 JLINK_ReadReg(FPS17)
T15FC 010:561.492 - 0.033ms returns 0x00000000
T15FC 010:561.516 JLINK_ReadReg(FPS18)
T15FC 010:561.537 - 0.030ms returns 0x00000000
T15FC 010:561.561 JLINK_ReadReg(FPS19)
T15FC 010:561.582 - 0.031ms returns 0x00000000
T15FC 010:561.606 JLINK_ReadReg(FPS20)
T15FC 010:561.626 - 0.029ms returns 0x00000000
T15FC 010:561.650 JLINK_ReadReg(FPS21)
T15FC 010:561.670 - 0.031ms returns 0x00000000
T15FC 010:561.695 JLINK_ReadReg(FPS22)
T15FC 010:561.716 - 0.030ms returns 0x00000000
T15FC 010:561.740 JLINK_ReadReg(FPS23)
T15FC 010:561.760 - 0.030ms returns 0x00000000
T15FC 010:561.784 JLINK_ReadReg(FPS24)
T15FC 010:561.805 - 0.030ms returns 0x00000000
T15FC 010:561.828 JLINK_ReadReg(FPS25)
T15FC 010:561.849 - 0.037ms returns 0x00000000
T15FC 010:561.883 JLINK_ReadReg(FPS26)
T15FC 010:561.906 - 0.032ms returns 0x00000000
T15FC 010:561.930 JLINK_ReadReg(FPS27)
T15FC 010:561.950 - 0.030ms returns 0x00000000
T15FC 010:561.974 JLINK_ReadReg(FPS28)
T15FC 010:561.995 - 0.030ms returns 0x00000000
T15FC 010:562.018 JLINK_ReadReg(FPS29)
T15FC 010:562.039 - 0.030ms returns 0x00000000
T15FC 010:562.062 JLINK_ReadReg(FPS30)
T15FC 010:562.083 - 0.030ms returns 0x00000000
T15FC 010:562.107 JLINK_ReadReg(FPS31)
T15FC 010:562.128 - 0.030ms returns 0x00000000
T2BFC 010:562.777 JLINK_ReadMemEx(0x20000558, 0x10 Bytes, Flags = 0x02000000)
T2BFC 010:562.821   CPU_ReadMem(64 bytes @ 0x20000540)
T2BFC 010:564.141    -- Updating C cache (64 bytes @ 0x20000540)
T2BFC 010:564.172    -- Read from C cache (16 bytes @ 0x20000558)
T2BFC 010:564.192   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
T2BFC 010:564.222 - 1.458ms returns 16 (0x10)
T2BFC 010:564.354 JLINK_ReadMemEx(0x20000568, 0x10 Bytes, Flags = 0x02000000)
T2BFC 010:564.373    -- Read from C cache (16 bytes @ 0x20000568)
T2BFC 010:564.393   Data:  00 00 00 00 00 00 00 00 00 00 00 00 01 00 00 00
T2BFC 010:564.411 - 0.062ms returns 16 (0x10)
T2BFC 010:565.490 JLINK_ReadMemEx(0x20000558, 0x10 Bytes, Flags = 0x02000000)
T2BFC 010:565.520    -- Read from C cache (16 bytes @ 0x20000558)
T2BFC 010:565.542   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
T2BFC 010:565.560 - 0.076ms returns 16 (0x10)
T2BFC 010:565.581 JLINK_ReadMemEx(0x20000568, 0x10 Bytes, Flags = 0x02000000)
T2BFC 010:565.596    -- Read from C cache (16 bytes @ 0x20000568)
T2BFC 010:565.615   Data:  00 00 00 00 00 00 00 00 00 00 00 00 01 00 00 00
T2BFC 010:565.633 - 0.058ms returns 16 (0x10)
T2BFC 010:565.661 JLINK_HasError()
T2BFC 010:565.678 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T2BFC 010:565.696   Data:  56 E2 CB 09
T2BFC 010:565.715   Debug reg: DWT_CYCCNT
T2BFC 010:565.733 - 0.061ms returns 1 (0x1)
T2BFC 010:567.804 JLINK_ReadMemEx(0x20000559, 0x1 Bytes, Flags = 0x02000000)
T2BFC 010:567.840    -- Read from C cache (1 bytes @ 0x20000559)
T2BFC 010:567.860   Data:  00
T2BFC 010:567.879 - 0.081ms returns 1 (0x1)
T2BFC 010:567.918 JLINK_ReadMemEx(0x20000559, 0x1 Bytes, Flags = 0x02000000)
T2BFC 010:567.935    -- Read from C cache (1 bytes @ 0x20000559)
T2BFC 010:567.953   Data:  00
T2BFC 010:567.972 - 0.060ms returns 1 (0x1)
T2BFC 010:574.664 JLINK_ReadMemEx(0x20000559, 0x1 Bytes, Flags = 0x02000000)
T2BFC 010:574.711    -- Read from C cache (1 bytes @ 0x20000559)
T2BFC 010:574.731   Data:  00
T2BFC 010:574.749 - 0.092ms returns 1 (0x1)
T2BFC 010:574.790 JLINK_ReadMemEx(0x20000559, 0x1 Bytes, Flags = 0x02000000)
T2BFC 010:574.807    -- Read from C cache (1 bytes @ 0x20000559)
T2BFC 010:574.825   Data:  00
T2BFC 010:574.844 - 0.059ms returns 1 (0x1)
T2BFC 010:583.717 JLINK_ReadMemEx(0x2000002C, 0x2 Bytes, Flags = 0x02000000)
T2BFC 010:583.764    -- Read from C cache (2 bytes @ 0x2000002C)
T2BFC 010:583.784   Data:  01 03
T2BFC 010:583.803 - 0.092ms returns 2 (0x2)
T2BFC 010:583.830 JLINK_WriteMemEx(0x20000559, 0x00000001 Bytes, Flags = 0x02000000)
T2BFC 010:583.844   Data:  01
T2BFC 010:583.873   CPU_WriteMem(1 bytes @ 0x20000559)
T2BFC 010:584.549 - 0.732ms returns 0x1
T2BFC 010:585.411 JLINK_ReadMemEx(0x20000558, 0x1 Bytes, Flags = 0x02000000)
T2BFC 010:585.453   CPU_ReadMem(64 bytes @ 0x20000540)
T2BFC 010:586.712    -- Updating C cache (64 bytes @ 0x20000540)
T2BFC 010:586.743    -- Read from C cache (1 bytes @ 0x20000558)
T2BFC 010:586.764   Data:  00
T2BFC 010:586.784 - 1.379ms returns 1 (0x1)
T2BFC 010:586.833 JLINK_ReadMemEx(0x20000558, 0x1 Bytes, Flags = 0x02000000)
T2BFC 010:586.853    -- Read from C cache (1 bytes @ 0x20000558)
T2BFC 010:586.873   Data:  00
T2BFC 010:586.892 - 0.065ms returns 1 (0x1)
T2BFC 010:595.425 JLINK_ReadMemEx(0x2000002C, 0x2 Bytes, Flags = 0x02000000)
T2BFC 010:595.472    -- Read from C cache (2 bytes @ 0x2000002C)
T2BFC 010:595.492   Data:  01 03
T2BFC 010:595.511 - 0.092ms returns 2 (0x2)
T2BFC 010:595.532 JLINK_WriteMemEx(0x20000558, 0x00000001 Bytes, Flags = 0x02000000)
T2BFC 010:595.596   Data:  03
T2BFC 010:595.625   CPU_WriteMem(1 bytes @ 0x20000558)
T2BFC 010:596.298 - 0.782ms returns 0x1
T15FC 014:238.962 JLINK_ReadMemEx(0x0800220C, 0x3C Bytes, Flags = 0x02000000)
T15FC 014:239.003    -- Read from flash cache (60 bytes @ 0x0800220C)
T15FC 014:239.024    -- Merging zombie BP[0]: 0x4A1B @ 0x08002210
T15FC 014:239.044    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 014:239.063    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 014:239.082   Data:  70 B5 88 B0 1B 4A 00 20 4F EA 0D 05 32 F8 10 10 ...
T15FC 014:239.101 - 0.146ms returns 60 (0x3C)
T15FC 014:239.118 JLINK_ReadMemEx(0x0800220C, 0x2 Bytes, Flags = 0x02000000)
T15FC 014:239.131    -- Read from flash cache (2 bytes @ 0x0800220C)
T15FC 014:239.150   Data:  70 B5
T15FC 014:239.169 - 0.058ms returns 2 (0x2)
T15FC 014:239.186 JLINK_ReadMemEx(0x0800220E, 0x2 Bytes, Flags = 0x02000000)
T15FC 014:239.200    -- Read from flash cache (2 bytes @ 0x0800220E)
T15FC 014:239.218   Data:  88 B0
T15FC 014:239.236 - 0.056ms returns 2 (0x2)
T15FC 014:239.253 JLINK_ReadMemEx(0x0800220C, 0x2 Bytes, Flags = 0x02000000)
T15FC 014:239.266    -- Read from flash cache (2 bytes @ 0x0800220C)
T15FC 014:239.285   Data:  70 B5
T15FC 014:239.304 - 0.057ms returns 2 (0x2)
T15FC 014:239.320 JLINK_HasError()
T15FC 014:239.340 JLINK_Step()
T15FC 014:240.029    -- Read from flash cache (2 bytes @ 0x0800220C)
T15FC 014:240.069   CPU_WriteMem(16 bytes @ 0x20000578)
T15FC 014:240.901   -- Simulated
T15FC 014:240.932 - 1.598ms returns 0
T15FC 014:240.956 JLINK_HasError()
T15FC 014:240.976 JLINK_ReadReg(R15 (PC))
T15FC 014:240.993 - 0.023ms returns 0x0800220E
T15FC 014:241.011 JLINK_ReadReg(XPSR)
T15FC 014:241.025 - 0.020ms returns 0x81000000
T15FC 014:241.767 JLINK_ReadMemEx(0x0800220E, 0x2 Bytes, Flags = 0x02000000)
T15FC 014:241.798    -- Read from flash cache (2 bytes @ 0x0800220E)
T15FC 014:241.818   Data:  88 B0
T15FC 014:241.837 - 0.076ms returns 2 (0x2)
T15FC 014:241.855 JLINK_ReadMemEx(0x08002210, 0x3C Bytes, Flags = 0x02000000)
T15FC 014:241.871    -- Read from flash cache (60 bytes @ 0x08002210)
T15FC 014:241.890    -- Merging zombie BP[0]: 0x4A1B @ 0x08002210
T15FC 014:241.910    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 014:241.932    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 014:241.958   Data:  1B 4A 00 20 4F EA 0D 05 32 F8 10 10 0B 0A 05 F8 ...
T15FC 014:241.976 - 0.127ms returns 60 (0x3C)
T15FC 014:241.992 JLINK_ReadMemEx(0x08002210, 0x2 Bytes, Flags = 0x02000000)
T15FC 014:242.005    -- Merging zombie BP[0]: 0x4A1B @ 0x08002210
T15FC 014:242.024    -- Merging zombie BP[0]: 0x4A1B @ 0x08002210
T15FC 014:242.042   Data:  1B 4A
T15FC 014:242.061 - 0.075ms returns 2 (0x2)
T15FC 014:242.079 JLINK_HasError()
T15FC 014:242.096 JLINK_Step()
T15FC 014:242.115    -- Read from flash cache (2 bytes @ 0x0800220E)
T15FC 014:242.137   -- Simulated
T15FC 014:242.156 - 0.067ms returns 0
T15FC 014:242.177 JLINK_HasError()
T15FC 014:242.200 JLINK_ReadReg(R15 (PC))
T15FC 014:242.216 - 0.022ms returns 0x08002210
T15FC 014:242.232 JLINK_ReadReg(XPSR)
T15FC 014:242.247 - 0.021ms returns 0x81000000
T15FC 014:242.343 JLINK_HasError()
T15FC 014:242.370 JLINK_ReadReg(R0)
T15FC 014:242.394 - 0.036ms returns 0x00000001
T15FC 014:242.420 JLINK_ReadReg(R1)
T15FC 014:242.441 - 0.032ms returns 0x00000001
T15FC 014:242.467 JLINK_ReadReg(R2)
T15FC 014:242.487 - 0.030ms returns 0x00000101
T15FC 014:242.511 JLINK_ReadReg(R3)
T15FC 014:242.532 - 0.031ms returns 0x00000000
T15FC 014:242.555 JLINK_ReadReg(R4)
T15FC 014:242.568 - 0.019ms returns 0x2000000C
T15FC 014:242.584 JLINK_ReadReg(R5)
T15FC 014:242.597 - 0.019ms returns 0x0800285C
T15FC 014:242.612 JLINK_ReadReg(R6)
T15FC 014:242.625 - 0.019ms returns 0x00000000
T15FC 014:242.640 JLINK_ReadReg(R7)
T15FC 014:242.653 - 0.019ms returns 0x00000000
T15FC 014:242.668 JLINK_ReadReg(R8)
T15FC 014:242.681 - 0.019ms returns 0x00000000
T15FC 014:242.696 JLINK_ReadReg(R9)
T15FC 014:242.713 - 0.024ms returns 0x00000000
T15FC 014:242.730 JLINK_ReadReg(R10)
T15FC 014:242.743 - 0.019ms returns 0x00000000
T15FC 014:242.758 JLINK_ReadReg(R11)
T15FC 014:242.771 - 0.019ms returns 0x00000000
T15FC 014:242.786 JLINK_ReadReg(R12)
T15FC 014:242.799 - 0.018ms returns 0x0000000E
T15FC 014:242.814 JLINK_ReadReg(R13 (SP))
T15FC 014:242.827 - 0.019ms returns 0x20000558
T15FC 014:242.842 JLINK_ReadReg(R14)
T15FC 014:242.855 - 0.019ms returns 0x080027FB
T15FC 014:242.870 JLINK_ReadReg(R15 (PC))
T15FC 014:242.883 - 0.019ms returns 0x08002210
T15FC 014:242.898 JLINK_ReadReg(XPSR)
T15FC 014:242.911 - 0.019ms returns 0x81000000
T15FC 014:242.926 JLINK_ReadReg(MSP)
T15FC 014:242.939 - 0.018ms returns 0x20000558
T15FC 014:242.954 JLINK_ReadReg(PSP)
T15FC 014:242.967 - 0.019ms returns 0x00000000
T15FC 014:242.982 JLINK_ReadReg(CFBP)
T15FC 014:242.995 - 0.018ms returns 0x00000000
T15FC 014:243.010 JLINK_ReadReg(FPSCR)
T15FC 014:243.023 - 0.019ms returns 0x00000000
T15FC 014:243.038 JLINK_ReadReg(FPS0)
T15FC 014:243.051 - 0.018ms returns 0x00000000
T15FC 014:243.066 JLINK_ReadReg(FPS1)
T15FC 014:243.079 - 0.018ms returns 0x00000000
T15FC 014:243.094 JLINK_ReadReg(FPS2)
T15FC 014:243.107 - 0.018ms returns 0x00000000
T15FC 014:243.122 JLINK_ReadReg(FPS3)
T15FC 014:243.135 - 0.019ms returns 0x00000000
T15FC 014:243.150 JLINK_ReadReg(FPS4)
T15FC 014:243.162 - 0.018ms returns 0x00000000
T15FC 014:243.178 JLINK_ReadReg(FPS5)
T15FC 014:243.190 - 0.019ms returns 0x00000000
T15FC 014:243.206 JLINK_ReadReg(FPS6)
T15FC 014:243.218 - 0.019ms returns 0x00000000
T15FC 014:243.234 JLINK_ReadReg(FPS7)
T15FC 014:243.246 - 0.018ms returns 0x00000000
T15FC 014:243.262 JLINK_ReadReg(FPS8)
T15FC 014:243.274 - 0.019ms returns 0x00000000
T15FC 014:243.289 JLINK_ReadReg(FPS9)
T15FC 014:243.302 - 0.018ms returns 0x00000000
T15FC 014:243.317 JLINK_ReadReg(FPS10)
T15FC 014:243.330 - 0.018ms returns 0x00000000
T15FC 014:243.345 JLINK_ReadReg(FPS11)
T15FC 014:243.358 - 0.018ms returns 0x00000000
T15FC 014:243.373 JLINK_ReadReg(FPS12)
T15FC 014:243.386 - 0.019ms returns 0x00000000
T15FC 014:243.408 JLINK_ReadReg(FPS13)
T15FC 014:243.434 - 0.033ms returns 0x00000000
T15FC 014:243.451 JLINK_ReadReg(FPS14)
T15FC 014:243.464 - 0.019ms returns 0x00000000
T15FC 014:243.479 JLINK_ReadReg(FPS15)
T15FC 014:243.492 - 0.019ms returns 0x00000000
T15FC 014:243.507 JLINK_ReadReg(FPS16)
T15FC 014:243.520 - 0.019ms returns 0x00000000
T15FC 014:243.536 JLINK_ReadReg(FPS17)
T15FC 014:243.548 - 0.018ms returns 0x00000000
T15FC 014:243.563 JLINK_ReadReg(FPS18)
T15FC 014:243.576 - 0.020ms returns 0x00000000
T15FC 014:243.593 JLINK_ReadReg(FPS19)
T15FC 014:243.606 - 0.019ms returns 0x00000000
T15FC 014:243.621 JLINK_ReadReg(FPS20)
T15FC 014:243.634 - 0.018ms returns 0x00000000
T15FC 014:243.649 JLINK_ReadReg(FPS21)
T15FC 014:243.662 - 0.018ms returns 0x00000000
T15FC 014:243.677 JLINK_ReadReg(FPS22)
T15FC 014:243.689 - 0.019ms returns 0x00000000
T15FC 014:243.704 JLINK_ReadReg(FPS23)
T15FC 014:243.717 - 0.018ms returns 0x00000000
T15FC 014:243.732 JLINK_ReadReg(FPS24)
T15FC 014:243.745 - 0.018ms returns 0x00000000
T15FC 014:243.760 JLINK_ReadReg(FPS25)
T15FC 014:243.773 - 0.018ms returns 0x00000000
T15FC 014:243.788 JLINK_ReadReg(FPS26)
T15FC 014:243.801 - 0.019ms returns 0x00000000
T15FC 014:243.816 JLINK_ReadReg(FPS27)
T15FC 014:243.829 - 0.018ms returns 0x00000000
T15FC 014:243.844 JLINK_ReadReg(FPS28)
T15FC 014:243.856 - 0.018ms returns 0x00000000
T15FC 014:243.872 JLINK_ReadReg(FPS29)
T15FC 014:243.884 - 0.018ms returns 0x00000000
T15FC 014:243.899 JLINK_ReadReg(FPS30)
T15FC 014:243.912 - 0.019ms returns 0x00000000
T15FC 014:243.927 JLINK_ReadReg(FPS31)
T15FC 014:243.940 - 0.018ms returns 0x00000000
T2BFC 014:244.132 JLINK_ReadMemEx(0x20000584, 0x4 Bytes, Flags = 0x02000000)
T2BFC 014:244.163   CPU_ReadMem(64 bytes @ 0x20000580)
T2BFC 014:245.794    -- Updating C cache (64 bytes @ 0x20000580)
T2BFC 014:245.822    -- Read from C cache (4 bytes @ 0x20000584)
T2BFC 014:245.843   Data:  FB 27 00 08
T2BFC 014:245.863 - 1.737ms returns 4 (0x4)
T2BFC 014:245.885 JLINK_ReadMemEx(0x20000578, 0x4 Bytes, Flags = 0x02000000)
T2BFC 014:245.904   CPU_ReadMem(64 bytes @ 0x20000540)
T2BFC 014:247.466    -- Updating C cache (64 bytes @ 0x20000540)
T2BFC 014:247.509    -- Read from C cache (4 bytes @ 0x20000578)
T2BFC 014:247.533   Data:  0C 00 00 20
T2BFC 014:247.552 - 1.672ms returns 4 (0x4)
T2BFC 014:247.571 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T2BFC 014:247.588    -- Read from C cache (4 bytes @ 0x2000057C)
T2BFC 014:247.607   Data:  5C 28 00 08
T2BFC 014:247.625 - 0.060ms returns 4 (0x4)
T2BFC 014:247.641 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T2BFC 014:247.655    -- Read from C cache (4 bytes @ 0x20000580)
T2BFC 014:247.673   Data:  00 00 00 00
T2BFC 014:247.691 - 0.056ms returns 4 (0x4)
T2BFC 014:247.707 JLINK_ReadMemEx(0x20000584, 0x4 Bytes, Flags = 0x02000000)
T2BFC 014:247.723    -- Read from C cache (4 bytes @ 0x20000584)
T2BFC 014:247.747   Data:  FB 27 00 08
T2BFC 014:247.765 - 0.064ms returns 4 (0x4)
T2BFC 014:247.799 JLINK_ReadMemEx(0x20000558, 0x10 Bytes, Flags = 0x02000000)
T2BFC 014:247.814    -- Read from C cache (16 bytes @ 0x20000558)
T2BFC 014:247.833   Data:  03 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00
T2BFC 014:247.851 - 0.058ms returns 16 (0x10)
T2BFC 014:247.887 JLINK_ReadMemEx(0x20000568, 0x10 Bytes, Flags = 0x02000000)
T2BFC 014:247.902    -- Read from C cache (16 bytes @ 0x20000568)
T2BFC 014:247.921   Data:  00 00 00 00 00 00 00 00 00 00 00 00 01 00 00 00
T2BFC 014:247.939 - 0.058ms returns 16 (0x10)
T2BFC 014:247.966 JLINK_HasError()
T2BFC 014:247.983 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T2BFC 014:247.998   Data:  5C E2 CB 09
T2BFC 014:248.017   Debug reg: DWT_CYCCNT
T2BFC 014:248.035 - 0.058ms returns 1 (0x1)
T2BFC 014:249.556 JLINK_ReadMemEx(0x20000559, 0x1 Bytes, Flags = 0x02000000)
T2BFC 014:249.584    -- Read from C cache (1 bytes @ 0x20000559)
T2BFC 014:249.603   Data:  01
T2BFC 014:249.622 - 0.072ms returns 1 (0x1)
T2BFC 014:250.085 JLINK_ReadMemEx(0x20000559, 0x1 Bytes, Flags = 0x02000000)
T2BFC 014:250.107    -- Read from C cache (1 bytes @ 0x20000559)
T2BFC 014:250.126   Data:  01
T2BFC 014:250.144 - 0.065ms returns 1 (0x1)
T2BFC 014:251.765 JLINK_ReadMemEx(0x2000002C, 0x2 Bytes, Flags = 0x02000000)
T2BFC 014:251.790    -- Read from C cache (2 bytes @ 0x2000002C)
T2BFC 014:251.809   Data:  01 03
T2BFC 014:251.828 - 0.069ms returns 2 (0x2)
T2BFC 014:251.847 JLINK_WriteMemEx(0x20000559, 0x00000001 Bytes, Flags = 0x02000000)
T2BFC 014:251.860   Data:  01
T2BFC 014:251.887   CPU_WriteMem(1 bytes @ 0x20000559)
T2BFC 014:252.862 - 1.036ms returns 0x1
T2BFC 014:253.578 JLINK_ReadMemEx(0x20000558, 0x1 Bytes, Flags = 0x02000000)
T2BFC 014:253.614   CPU_ReadMem(64 bytes @ 0x20000540)
T2BFC 014:255.224    -- Updating C cache (64 bytes @ 0x20000540)
T2BFC 014:255.252    -- Read from C cache (1 bytes @ 0x20000558)
T2BFC 014:255.271   Data:  03
T2BFC 014:255.291 - 1.719ms returns 1 (0x1)
T2BFC 014:257.079 JLINK_ReadMemEx(0x2000002C, 0x2 Bytes, Flags = 0x02000000)
T2BFC 014:257.110    -- Read from C cache (2 bytes @ 0x2000002C)
T2BFC 014:257.130   Data:  01 03
T2BFC 014:257.149 - 0.076ms returns 2 (0x2)
T2BFC 014:257.168 JLINK_WriteMemEx(0x20000558, 0x00000001 Bytes, Flags = 0x02000000)
T2BFC 014:257.182   Data:  03
T2BFC 014:257.208   CPU_WriteMem(1 bytes @ 0x20000558)
T2BFC 014:258.319 - 1.172ms returns 0x1
T2BFC 014:261.950 JLINK_ReadMemEx(0x08002210, 0x3C Bytes, Flags = 0x02000000)
T2BFC 014:261.990    -- Read from flash cache (60 bytes @ 0x08002210)
T2BFC 014:262.010    -- Merging zombie BP[0]: 0x4A1B @ 0x08002210
T2BFC 014:262.029    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T2BFC 014:262.047    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T2BFC 014:262.066   Data:  1B 4A 00 20 4F EA 0D 05 32 F8 10 10 0B 0A 05 F8 ...
T2BFC 014:262.084 - 0.140ms returns 60 (0x3C)
T2BFC 014:262.101 JLINK_ReadMemEx(0x08002210, 0x2 Bytes, Flags = 0x02000000)
T2BFC 014:262.117    -- Merging zombie BP[0]: 0x4A1B @ 0x08002210
T2BFC 014:262.138    -- Merging zombie BP[0]: 0x4A1B @ 0x08002210
T2BFC 014:262.156   Data:  1B 4A
T2BFC 014:262.175 - 0.080ms returns 2 (0x2)
T2BFC 014:262.191 JLINK_ReadMemEx(0x08002212, 0x2 Bytes, Flags = 0x02000000)
T2BFC 014:262.204    -- Read from flash cache (2 bytes @ 0x08002212)
T2BFC 014:262.222   Data:  00 20
T2BFC 014:262.241 - 0.057ms returns 2 (0x2)
T15FC 015:190.766 JLINK_ReadMemEx(0x08002210, 0x2 Bytes, Flags = 0x02000000)
T15FC 015:190.806    -- Merging zombie BP[0]: 0x4A1B @ 0x08002210
T15FC 015:190.829    -- Merging zombie BP[0]: 0x4A1B @ 0x08002210
T15FC 015:190.849   Data:  1B 4A
T15FC 015:190.868 - 0.108ms returns 2 (0x2)
T15FC 015:190.886 JLINK_HasError()
T15FC 015:190.905 JLINK_Step()
T15FC 015:191.610    -- Merging zombie BP[0]: 0x4A1B @ 0x08002210
T15FC 015:191.653    -- Merging zombie BP[0]: 0x4A1B @ 0x08002210
T15FC 015:191.681    -- Read from flash cache (4 bytes @ 0x08002280)
T15FC 015:191.708   -- Simulated
T15FC 015:191.733 - 0.837ms returns 0
T15FC 015:191.759 JLINK_HasError()
T15FC 015:191.781 JLINK_ReadReg(R15 (PC))
T15FC 015:191.801 - 0.028ms returns 0x08002212
T15FC 015:191.822 JLINK_ReadReg(XPSR)
T15FC 015:191.843 - 0.029ms returns 0x81000000
T15FC 015:193.831 JLINK_HasError()
T15FC 015:193.881 JLINK_ReadReg(R0)
T15FC 015:193.900 - 0.025ms returns 0x00000001
T15FC 015:193.916 JLINK_ReadReg(R1)
T15FC 015:193.930 - 0.020ms returns 0x00000001
T15FC 015:193.946 JLINK_ReadReg(R2)
T15FC 015:193.958 - 0.019ms returns 0x2000002C
T15FC 015:193.974 JLINK_ReadReg(R3)
T15FC 015:193.987 - 0.019ms returns 0x00000000
T15FC 015:194.002 JLINK_ReadReg(R4)
T15FC 015:194.015 - 0.019ms returns 0x2000000C
T15FC 015:194.030 JLINK_ReadReg(R5)
T15FC 015:194.043 - 0.019ms returns 0x0800285C
T15FC 015:194.058 JLINK_ReadReg(R6)
T15FC 015:194.071 - 0.019ms returns 0x00000000
T15FC 015:194.086 JLINK_ReadReg(R7)
T15FC 015:194.099 - 0.019ms returns 0x00000000
T15FC 015:194.115 JLINK_ReadReg(R8)
T15FC 015:194.128 - 0.019ms returns 0x00000000
T15FC 015:194.143 JLINK_ReadReg(R9)
T15FC 015:194.156 - 0.019ms returns 0x00000000
T15FC 015:194.171 JLINK_ReadReg(R10)
T15FC 015:194.184 - 0.019ms returns 0x00000000
T15FC 015:194.199 JLINK_ReadReg(R11)
T15FC 015:194.212 - 0.019ms returns 0x00000000
T15FC 015:194.227 JLINK_ReadReg(R12)
T15FC 015:194.240 - 0.019ms returns 0x0000000E
T15FC 015:194.255 JLINK_ReadReg(R13 (SP))
T15FC 015:194.268 - 0.019ms returns 0x20000558
T15FC 015:194.283 JLINK_ReadReg(R14)
T15FC 015:194.296 - 0.019ms returns 0x080027FB
T15FC 015:194.312 JLINK_ReadReg(R15 (PC))
T15FC 015:194.325 - 0.019ms returns 0x08002212
T15FC 015:194.340 JLINK_ReadReg(XPSR)
T15FC 015:194.353 - 0.019ms returns 0x81000000
T15FC 015:194.368 JLINK_ReadReg(MSP)
T15FC 015:194.381 - 0.020ms returns 0x20000558
T15FC 015:194.398 JLINK_ReadReg(PSP)
T15FC 015:194.411 - 0.019ms returns 0x00000000
T15FC 015:194.426 JLINK_ReadReg(CFBP)
T15FC 015:194.439 - 0.018ms returns 0x00000000
T15FC 015:194.454 JLINK_ReadReg(FPSCR)
T15FC 015:194.467 - 0.019ms returns 0x00000000
T15FC 015:194.482 JLINK_ReadReg(FPS0)
T15FC 015:194.495 - 0.019ms returns 0x00000000
T15FC 015:194.510 JLINK_ReadReg(FPS1)
T15FC 015:194.523 - 0.019ms returns 0x00000000
T15FC 015:194.538 JLINK_ReadReg(FPS2)
T15FC 015:194.551 - 0.019ms returns 0x00000000
T15FC 015:194.567 JLINK_ReadReg(FPS3)
T15FC 015:194.580 - 0.018ms returns 0x00000000
T15FC 015:194.595 JLINK_ReadReg(FPS4)
T15FC 015:194.608 - 0.018ms returns 0x00000000
T15FC 015:194.623 JLINK_ReadReg(FPS5)
T15FC 015:194.636 - 0.018ms returns 0x00000000
T15FC 015:194.651 JLINK_ReadReg(FPS6)
T15FC 015:194.664 - 0.020ms returns 0x00000000
T15FC 015:194.680 JLINK_ReadReg(FPS7)
T15FC 015:194.692 - 0.019ms returns 0x00000000
T15FC 015:194.708 JLINK_ReadReg(FPS8)
T15FC 015:194.720 - 0.018ms returns 0x00000000
T15FC 015:194.735 JLINK_ReadReg(FPS9)
T15FC 015:194.748 - 0.019ms returns 0x00000000
T15FC 015:194.763 JLINK_ReadReg(FPS10)
T15FC 015:194.777 - 0.019ms returns 0x00000000
T15FC 015:194.792 JLINK_ReadReg(FPS11)
T15FC 015:194.810 - 0.025ms returns 0x00000000
T15FC 015:194.832 JLINK_ReadReg(FPS12)
T15FC 015:194.856 - 0.034ms returns 0x00000000
T15FC 015:194.883 JLINK_ReadReg(FPS13)
T15FC 015:194.899 - 0.022ms returns 0x00000000
T15FC 015:194.916 JLINK_ReadReg(FPS14)
T15FC 015:194.929 - 0.019ms returns 0x00000000
T15FC 015:194.944 JLINK_ReadReg(FPS15)
T15FC 015:194.957 - 0.018ms returns 0x00000000
T15FC 015:194.972 JLINK_ReadReg(FPS16)
T15FC 015:194.985 - 0.019ms returns 0x00000000
T15FC 015:195.000 JLINK_ReadReg(FPS17)
T15FC 015:195.013 - 0.018ms returns 0x00000000
T15FC 015:195.030 JLINK_ReadReg(FPS18)
T15FC 015:195.044 - 0.019ms returns 0x00000000
T15FC 015:195.059 JLINK_ReadReg(FPS19)
T15FC 015:195.071 - 0.018ms returns 0x00000000
T15FC 015:195.087 JLINK_ReadReg(FPS20)
T15FC 015:195.099 - 0.018ms returns 0x00000000
T15FC 015:195.114 JLINK_ReadReg(FPS21)
T15FC 015:195.127 - 0.018ms returns 0x00000000
T15FC 015:195.142 JLINK_ReadReg(FPS22)
T15FC 015:195.155 - 0.019ms returns 0x00000000
T15FC 015:195.170 JLINK_ReadReg(FPS23)
T15FC 015:195.183 - 0.018ms returns 0x00000000
T15FC 015:195.198 JLINK_ReadReg(FPS24)
T15FC 015:195.210 - 0.018ms returns 0x00000000
T15FC 015:195.226 JLINK_ReadReg(FPS25)
T15FC 015:195.238 - 0.018ms returns 0x00000000
T15FC 015:195.253 JLINK_ReadReg(FPS26)
T15FC 015:195.266 - 0.018ms returns 0x00000000
T15FC 015:195.281 JLINK_ReadReg(FPS27)
T15FC 015:195.294 - 0.018ms returns 0x00000000
T15FC 015:195.309 JLINK_ReadReg(FPS28)
T15FC 015:195.321 - 0.018ms returns 0x00000000
T15FC 015:195.336 JLINK_ReadReg(FPS29)
T15FC 015:195.349 - 0.018ms returns 0x00000000
T15FC 015:195.364 JLINK_ReadReg(FPS30)
T15FC 015:195.377 - 0.019ms returns 0x00000000
T15FC 015:195.392 JLINK_ReadReg(FPS31)
T15FC 015:195.405 - 0.018ms returns 0x00000000
T2BFC 015:195.593 JLINK_ReadMemEx(0x20000584, 0x4 Bytes, Flags = 0x02000000)
T2BFC 015:195.623    -- Read from C cache (4 bytes @ 0x20000584)
T2BFC 015:195.643   Data:  FB 27 00 08
T2BFC 015:195.662 - 0.075ms returns 4 (0x4)
T2BFC 015:195.681 JLINK_ReadMemEx(0x20000578, 0x4 Bytes, Flags = 0x02000000)
T2BFC 015:195.700   CPU_ReadMem(64 bytes @ 0x20000540)
T2BFC 015:197.339    -- Updating C cache (64 bytes @ 0x20000540)
T2BFC 015:197.367    -- Read from C cache (4 bytes @ 0x20000578)
T2BFC 015:197.386   Data:  0C 00 00 20
T2BFC 015:197.405 - 1.730ms returns 4 (0x4)
T2BFC 015:197.424 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T2BFC 015:197.440    -- Read from C cache (4 bytes @ 0x2000057C)
T2BFC 015:197.458   Data:  5C 28 00 08
T2BFC 015:197.477 - 0.059ms returns 4 (0x4)
T2BFC 015:197.492 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T2BFC 015:197.506    -- Read from C cache (4 bytes @ 0x20000580)
T2BFC 015:197.524   Data:  00 00 00 00
T2BFC 015:197.543 - 0.056ms returns 4 (0x4)
T2BFC 015:197.558 JLINK_ReadMemEx(0x20000584, 0x4 Bytes, Flags = 0x02000000)
T2BFC 015:197.572    -- Read from C cache (4 bytes @ 0x20000584)
T2BFC 015:197.590   Data:  FB 27 00 08
T2BFC 015:197.608 - 0.056ms returns 4 (0x4)
T2BFC 015:197.643 JLINK_ReadMemEx(0x20000558, 0x10 Bytes, Flags = 0x02000000)
T2BFC 015:197.658    -- Read from C cache (16 bytes @ 0x20000558)
T2BFC 015:197.677   Data:  03 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00
T2BFC 015:197.695 - 0.058ms returns 16 (0x10)
T2BFC 015:197.722 JLINK_ReadMemEx(0x20000568, 0x10 Bytes, Flags = 0x02000000)
T2BFC 015:197.737    -- Read from C cache (16 bytes @ 0x20000568)
T2BFC 015:197.756   Data:  00 00 00 00 00 00 00 00 00 00 00 00 01 00 00 00
T2BFC 015:197.774 - 0.057ms returns 16 (0x10)
T2BFC 015:197.812 JLINK_HasError()
T2BFC 015:197.829 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T2BFC 015:197.847   Data:  5E E2 CB 09
T2BFC 015:197.866   Debug reg: DWT_CYCCNT
T2BFC 015:197.884 - 0.060ms returns 1 (0x1)
T2BFC 015:199.401 JLINK_ReadMemEx(0x2000055B, 0x1 Bytes, Flags = 0x02000000)
T2BFC 015:199.430    -- Read from C cache (1 bytes @ 0x2000055B)
T2BFC 015:199.450   Data:  00
T2BFC 015:199.469 - 0.073ms returns 1 (0x1)
T2BFC 015:199.938 JLINK_ReadMemEx(0x2000055B, 0x1 Bytes, Flags = 0x02000000)
T2BFC 015:199.965    -- Read from C cache (1 bytes @ 0x2000055B)
T2BFC 015:199.984   Data:  00
T2BFC 015:200.003 - 0.071ms returns 1 (0x1)
T2BFC 015:201.613 JLINK_ReadMemEx(0x2000002E, 0x2 Bytes, Flags = 0x02000000)
T2BFC 015:201.639    -- Read from C cache (2 bytes @ 0x2000002E)
T2BFC 015:201.658   Data:  02 03
T2BFC 015:201.677 - 0.070ms returns 2 (0x2)
T2BFC 015:201.696 JLINK_WriteMemEx(0x2000055B, 0x00000001 Bytes, Flags = 0x02000000)
T2BFC 015:201.709   Data:  02
T2BFC 015:201.737   CPU_WriteMem(1 bytes @ 0x2000055B)
T2BFC 015:202.753 - 1.071ms returns 0x1
T2BFC 015:203.091 JLINK_ReadMemEx(0x2000055A, 0x1 Bytes, Flags = 0x02000000)
T2BFC 015:203.116   CPU_ReadMem(64 bytes @ 0x20000540)
T2BFC 015:204.649    -- Updating C cache (64 bytes @ 0x20000540)
T2BFC 015:204.722    -- Read from C cache (1 bytes @ 0x2000055A)
T2BFC 015:204.772   Data:  00
T2BFC 015:204.819 - 1.740ms returns 1 (0x1)
T2BFC 015:206.784 JLINK_ReadMemEx(0x2000002E, 0x2 Bytes, Flags = 0x02000000)
T2BFC 015:206.825    -- Read from C cache (2 bytes @ 0x2000002E)
T2BFC 015:206.844   Data:  02 03
T2BFC 015:206.863 - 0.085ms returns 2 (0x2)
T2BFC 015:206.883 JLINK_WriteMemEx(0x2000055A, 0x00000001 Bytes, Flags = 0x02000000)
T2BFC 015:206.897   Data:  03
T2BFC 015:206.926   CPU_WriteMem(1 bytes @ 0x2000055A)
T2BFC 015:207.884 - 1.021ms returns 0x1
T2BFC 015:211.222 JLINK_ReadMemEx(0x08002212, 0x2 Bytes, Flags = 0x02000000)
T2BFC 015:211.259    -- Read from flash cache (2 bytes @ 0x08002212)
T2BFC 015:211.278   Data:  00 20
T2BFC 015:211.297 - 0.082ms returns 2 (0x2)
T2BFC 015:211.314 JLINK_ReadMemEx(0x08002214, 0x3C Bytes, Flags = 0x02000000)
T2BFC 015:211.328    -- Read from flash cache (60 bytes @ 0x08002214)
T2BFC 015:211.346    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T2BFC 015:211.365    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T2BFC 015:211.384   Data:  4F EA 0D 05 32 F8 10 10 0B 0A 05 F8 10 30 05 EB ...
T2BFC 015:211.402 - 0.093ms returns 60 (0x3C)
T2BFC 015:211.418 JLINK_ReadMemEx(0x08002214, 0x2 Bytes, Flags = 0x02000000)
T2BFC 015:211.431    -- Read from flash cache (2 bytes @ 0x08002214)
T2BFC 015:211.449   Data:  4F EA
T2BFC 015:211.467 - 0.055ms returns 2 (0x2)
T15FC 015:958.845 JLINK_ReadMemEx(0x08002212, 0x2 Bytes, Flags = 0x02000000)
T15FC 015:958.918    -- Read from flash cache (2 bytes @ 0x08002212)
T15FC 015:958.944   Data:  00 20
T15FC 015:958.963 - 0.124ms returns 2 (0x2)
T15FC 015:958.981 JLINK_HasError()
T15FC 015:959.000 JLINK_Step()
T15FC 015:959.740    -- Read from flash cache (2 bytes @ 0x08002212)
T15FC 015:959.781   -- Simulated
T15FC 015:959.815 - 0.823ms returns 0
T15FC 015:959.837 JLINK_HasError()
T15FC 015:959.854 JLINK_ReadReg(R15 (PC))
T15FC 015:959.870 - 0.022ms returns 0x08002214
T15FC 015:959.886 JLINK_ReadReg(XPSR)
T15FC 015:959.899 - 0.019ms returns 0x41000000
T15FC 015:960.294 JLINK_HasError()
T15FC 015:960.323 JLINK_ReadReg(R0)
T15FC 015:960.340 - 0.023ms returns 0x00000000
T15FC 015:960.356 JLINK_ReadReg(R1)
T15FC 015:960.370 - 0.020ms returns 0x00000001
T15FC 015:960.386 JLINK_ReadReg(R2)
T15FC 015:960.400 - 0.020ms returns 0x2000002C
T15FC 015:960.416 JLINK_ReadReg(R3)
T15FC 015:960.430 - 0.020ms returns 0x00000000
T15FC 015:960.446 JLINK_ReadReg(R4)
T15FC 015:960.459 - 0.020ms returns 0x2000000C
T15FC 015:960.475 JLINK_ReadReg(R5)
T15FC 015:960.489 - 0.020ms returns 0x0800285C
T15FC 015:960.504 JLINK_ReadReg(R6)
T15FC 015:960.518 - 0.020ms returns 0x00000000
T15FC 015:960.534 JLINK_ReadReg(R7)
T15FC 015:960.547 - 0.019ms returns 0x00000000
T15FC 015:960.563 JLINK_ReadReg(R8)
T15FC 015:960.576 - 0.020ms returns 0x00000000
T15FC 015:960.592 JLINK_ReadReg(R9)
T15FC 015:960.605 - 0.019ms returns 0x00000000
T15FC 015:960.621 JLINK_ReadReg(R10)
T15FC 015:960.634 - 0.019ms returns 0x00000000
T15FC 015:960.650 JLINK_ReadReg(R11)
T15FC 015:960.663 - 0.019ms returns 0x00000000
T15FC 015:960.679 JLINK_ReadReg(R12)
T15FC 015:960.692 - 0.019ms returns 0x0000000E
T15FC 015:960.712 JLINK_ReadReg(R13 (SP))
T15FC 015:960.727 - 0.022ms returns 0x20000558
T15FC 015:960.743 JLINK_ReadReg(R14)
T15FC 015:960.757 - 0.019ms returns 0x080027FB
T15FC 015:960.775 JLINK_ReadReg(R15 (PC))
T15FC 015:960.801 - 0.038ms returns 0x08002214
T15FC 015:960.825 JLINK_ReadReg(XPSR)
T15FC 015:960.840 - 0.021ms returns 0x41000000
T15FC 015:960.856 JLINK_ReadReg(MSP)
T15FC 015:960.869 - 0.019ms returns 0x20000558
T15FC 015:960.884 JLINK_ReadReg(PSP)
T15FC 015:960.897 - 0.019ms returns 0x00000000
T15FC 015:960.913 JLINK_ReadReg(CFBP)
T15FC 015:960.926 - 0.019ms returns 0x00000000
T15FC 015:960.941 JLINK_ReadReg(FPSCR)
T15FC 015:960.954 - 0.019ms returns 0x00000000
T15FC 015:960.970 JLINK_ReadReg(FPS0)
T15FC 015:960.983 - 0.019ms returns 0x00000000
T15FC 015:960.998 JLINK_ReadReg(FPS1)
T15FC 015:961.011 - 0.019ms returns 0x00000000
T15FC 015:961.027 JLINK_ReadReg(FPS2)
T15FC 015:961.039 - 0.019ms returns 0x00000000
T15FC 015:961.055 JLINK_ReadReg(FPS3)
T15FC 015:961.067 - 0.019ms returns 0x00000000
T15FC 015:961.083 JLINK_ReadReg(FPS4)
T15FC 015:961.096 - 0.019ms returns 0x00000000
T15FC 015:961.111 JLINK_ReadReg(FPS5)
T15FC 015:961.124 - 0.019ms returns 0x00000000
T15FC 015:961.139 JLINK_ReadReg(FPS6)
T15FC 015:961.152 - 0.019ms returns 0x00000000
T15FC 015:961.168 JLINK_ReadReg(FPS7)
T15FC 015:961.181 - 0.019ms returns 0x00000000
T15FC 015:961.196 JLINK_ReadReg(FPS8)
T15FC 015:961.209 - 0.019ms returns 0x00000000
T15FC 015:961.224 JLINK_ReadReg(FPS9)
T15FC 015:961.237 - 0.019ms returns 0x00000000
T15FC 015:961.252 JLINK_ReadReg(FPS10)
T15FC 015:961.265 - 0.019ms returns 0x00000000
T15FC 015:961.280 JLINK_ReadReg(FPS11)
T15FC 015:961.293 - 0.019ms returns 0x00000000
T15FC 015:961.309 JLINK_ReadReg(FPS12)
T15FC 015:961.322 - 0.019ms returns 0x00000000
T15FC 015:961.337 JLINK_ReadReg(FPS13)
T15FC 015:961.350 - 0.019ms returns 0x00000000
T15FC 015:961.365 JLINK_ReadReg(FPS14)
T15FC 015:961.379 - 0.019ms returns 0x00000000
T15FC 015:961.394 JLINK_ReadReg(FPS15)
T15FC 015:961.407 - 0.019ms returns 0x00000000
T15FC 015:961.422 JLINK_ReadReg(FPS16)
T15FC 015:961.435 - 0.019ms returns 0x00000000
T15FC 015:961.450 JLINK_ReadReg(FPS17)
T15FC 015:961.463 - 0.019ms returns 0x00000000
T15FC 015:961.479 JLINK_ReadReg(FPS18)
T15FC 015:961.492 - 0.019ms returns 0x00000000
T15FC 015:961.507 JLINK_ReadReg(FPS19)
T15FC 015:961.522 - 0.021ms returns 0x00000000
T15FC 015:961.537 JLINK_ReadReg(FPS20)
T15FC 015:961.550 - 0.019ms returns 0x00000000
T15FC 015:961.565 JLINK_ReadReg(FPS21)
T15FC 015:961.578 - 0.019ms returns 0x00000000
T15FC 015:961.594 JLINK_ReadReg(FPS22)
T15FC 015:961.607 - 0.019ms returns 0x00000000
T15FC 015:961.623 JLINK_ReadReg(FPS23)
T15FC 015:961.635 - 0.019ms returns 0x00000000
T15FC 015:961.651 JLINK_ReadReg(FPS24)
T15FC 015:961.664 - 0.019ms returns 0x00000000
T15FC 015:961.679 JLINK_ReadReg(FPS25)
T15FC 015:961.692 - 0.019ms returns 0x00000000
T15FC 015:961.707 JLINK_ReadReg(FPS26)
T15FC 015:961.720 - 0.019ms returns 0x00000000
T15FC 015:961.736 JLINK_ReadReg(FPS27)
T15FC 015:961.748 - 0.019ms returns 0x00000000
T15FC 015:961.766 JLINK_ReadReg(FPS28)
T15FC 015:961.790 - 0.037ms returns 0x00000000
T15FC 015:961.816 JLINK_ReadReg(FPS29)
T15FC 015:961.831 - 0.021ms returns 0x00000000
T15FC 015:961.847 JLINK_ReadReg(FPS30)
T15FC 015:961.860 - 0.019ms returns 0x00000000
T15FC 015:961.875 JLINK_ReadReg(FPS31)
T15FC 015:961.888 - 0.019ms returns 0x00000000
T2BFC 015:962.366 JLINK_ReadMemEx(0x20000584, 0x4 Bytes, Flags = 0x02000000)
T2BFC 015:962.406    -- Read from C cache (4 bytes @ 0x20000584)
T2BFC 015:962.436   Data:  FB 27 00 08
T2BFC 015:962.462 - 0.105ms returns 4 (0x4)
T2BFC 015:962.488 JLINK_ReadMemEx(0x20000578, 0x4 Bytes, Flags = 0x02000000)
T2BFC 015:962.515   CPU_ReadMem(64 bytes @ 0x20000540)
T2BFC 015:963.825    -- Updating C cache (64 bytes @ 0x20000540)
T2BFC 015:963.856    -- Read from C cache (4 bytes @ 0x20000578)
T2BFC 015:963.875   Data:  0C 00 00 20
T2BFC 015:963.894 - 1.412ms returns 4 (0x4)
T2BFC 015:963.915 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T2BFC 015:963.937    -- Read from C cache (4 bytes @ 0x2000057C)
T2BFC 015:963.956   Data:  5C 28 00 08
T2BFC 015:963.975 - 0.065ms returns 4 (0x4)
T2BFC 015:963.991 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T2BFC 015:964.005    -- Read from C cache (4 bytes @ 0x20000580)
T2BFC 015:964.024   Data:  00 00 00 00
T2BFC 015:964.043 - 0.058ms returns 4 (0x4)
T2BFC 015:964.058 JLINK_ReadMemEx(0x20000584, 0x4 Bytes, Flags = 0x02000000)
T2BFC 015:964.072    -- Read from C cache (4 bytes @ 0x20000584)
T2BFC 015:964.091   Data:  FB 27 00 08
T2BFC 015:964.109 - 0.057ms returns 4 (0x4)
T2BFC 015:964.144 JLINK_ReadMemEx(0x20000558, 0x10 Bytes, Flags = 0x02000000)
T2BFC 015:964.159    -- Read from C cache (16 bytes @ 0x20000558)
T2BFC 015:964.179   Data:  03 01 03 02 00 00 00 00 00 00 00 00 00 00 00 00
T2BFC 015:964.197 - 0.059ms returns 16 (0x10)
T2BFC 015:964.232 JLINK_ReadMemEx(0x20000568, 0x10 Bytes, Flags = 0x02000000)
T2BFC 015:964.247    -- Read from C cache (16 bytes @ 0x20000568)
T2BFC 015:964.267   Data:  00 00 00 00 00 00 00 00 00 00 00 00 01 00 00 00
T2BFC 015:964.285 - 0.059ms returns 16 (0x10)
T2BFC 015:964.323 JLINK_HasError()
T2BFC 015:964.341 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T2BFC 015:964.358   Data:  5F E2 CB 09
T2BFC 015:964.377   Debug reg: DWT_CYCCNT
T2BFC 015:964.395 - 0.060ms returns 1 (0x1)
T2BFC 015:965.959 JLINK_ReadMemEx(0x20000559, 0x1 Bytes, Flags = 0x02000000)
T2BFC 015:965.997    -- Read from C cache (1 bytes @ 0x20000559)
T2BFC 015:966.017   Data:  01
T2BFC 015:966.036 - 0.083ms returns 1 (0x1)
T2BFC 015:966.299 JLINK_ReadMemEx(0x20000559, 0x1 Bytes, Flags = 0x02000000)
T2BFC 015:966.322    -- Read from C cache (1 bytes @ 0x20000559)
T2BFC 015:966.341   Data:  01
T2BFC 015:966.361 - 0.067ms returns 1 (0x1)
T2BFC 015:967.929 JLINK_ReadMemEx(0x2000002C, 0x2 Bytes, Flags = 0x02000000)
T2BFC 015:967.976    -- Read from C cache (2 bytes @ 0x2000002C)
T2BFC 015:968.005   Data:  01 03
T2BFC 015:968.032 - 0.112ms returns 2 (0x2)
T2BFC 015:968.062 JLINK_WriteMemEx(0x20000559, 0x00000001 Bytes, Flags = 0x02000000)
T2BFC 015:968.082   Data:  01
T2BFC 015:968.121   CPU_WriteMem(1 bytes @ 0x20000559)
T2BFC 015:968.960 - 0.914ms returns 0x1
T2BFC 015:969.354 JLINK_ReadMemEx(0x20000558, 0x1 Bytes, Flags = 0x02000000)
T2BFC 015:969.382   CPU_ReadMem(64 bytes @ 0x20000540)
T2BFC 015:970.699    -- Updating C cache (64 bytes @ 0x20000540)
T2BFC 015:970.729    -- Read from C cache (1 bytes @ 0x20000558)
T2BFC 015:970.754   Data:  03
T2BFC 015:970.774 - 1.426ms returns 1 (0x1)
T2BFC 015:972.621 JLINK_ReadMemEx(0x2000002C, 0x2 Bytes, Flags = 0x02000000)
T2BFC 015:972.686    -- Read from C cache (2 bytes @ 0x2000002C)
T2BFC 015:972.723   Data:  01 03
T2BFC 015:972.753 - 0.139ms returns 2 (0x2)
T2BFC 015:972.775 JLINK_WriteMemEx(0x20000558, 0x00000001 Bytes, Flags = 0x02000000)
T2BFC 015:972.789   Data:  03
T2BFC 015:972.824   CPU_WriteMem(1 bytes @ 0x20000558)
T2BFC 015:973.674 - 0.915ms returns 0x1
T15FC 029:782.643 JLINK_ReadMemEx(0x08002214, 0x3C Bytes, Flags = 0x02000000)
T15FC 029:782.678    -- Read from flash cache (60 bytes @ 0x08002214)
T15FC 029:782.698    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 029:782.718    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 029:782.738   Data:  4F EA 0D 05 32 F8 10 10 0B 0A 05 F8 10 30 05 EB ...
T15FC 029:782.756 - 0.119ms returns 60 (0x3C)
T15FC 029:782.773 JLINK_ReadMemEx(0x08002214, 0x2 Bytes, Flags = 0x02000000)
T15FC 029:782.787    -- Read from flash cache (2 bytes @ 0x08002214)
T15FC 029:782.806   Data:  4F EA
T15FC 029:782.840 - 0.075ms returns 2 (0x2)
T15FC 029:782.860 JLINK_ReadMemEx(0x08002216, 0x2 Bytes, Flags = 0x02000000)
T15FC 029:782.874    -- Read from flash cache (2 bytes @ 0x08002216)
T15FC 029:782.892   Data:  0D 05
T15FC 029:782.911 - 0.057ms returns 2 (0x2)
T15FC 029:782.927 JLINK_ReadMemEx(0x08002214, 0x2 Bytes, Flags = 0x02000000)
T15FC 029:782.940    -- Read from flash cache (2 bytes @ 0x08002214)
T15FC 029:782.963   Data:  4F EA
T15FC 029:782.981 - 0.060ms returns 2 (0x2)
T15FC 029:782.997 JLINK_HasError()
T15FC 029:783.015 JLINK_Step()
T15FC 029:783.724    -- Read from flash cache (2 bytes @ 0x08002214)
T15FC 029:783.755    -- Read from flash cache (2 bytes @ 0x08002216)
T15FC 029:783.774   -- Not simulated
T15FC 029:783.804   CPU_WriteMem(4 bytes @ 0xE0002008)
T15FC 029:783.838   CPU_WriteMem(4 bytes @ 0xE000200C)
T15FC 029:783.865   CPU_WriteMem(4 bytes @ 0xE0002010)
T15FC 029:783.885   CPU_WriteMem(4 bytes @ 0xE0002014)
T15FC 029:783.903   CPU_WriteMem(4 bytes @ 0xE0002018)
T15FC 029:783.922   CPU_WriteMem(4 bytes @ 0xE000201C)
T15FC 029:786.592   CPU_WriteMem(4 bytes @ 0xE0001004)
T15FC 029:794.439 - 11.450ms returns 0
T15FC 029:794.481 JLINK_HasError()
T15FC 029:794.499 JLINK_ReadReg(R15 (PC))
T15FC 029:794.515 - 0.022ms returns 0x08002218
T15FC 029:794.531 JLINK_ReadReg(XPSR)
T15FC 029:794.544 - 0.019ms returns 0x41000000
T15FC 029:794.570 JLINK_ReadMemEx(0x08002216, 0x2 Bytes, Flags = 0x02000000)
T15FC 029:794.586    -- Read from flash cache (2 bytes @ 0x08002216)
T15FC 029:794.605   Data:  0D 05
T15FC 029:794.624 - 0.060ms returns 2 (0x2)
T15FC 029:794.640 JLINK_ReadMemEx(0x08002218, 0x3C Bytes, Flags = 0x02000000)
T15FC 029:794.653    -- Read from flash cache (60 bytes @ 0x08002218)
T15FC 029:794.672    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 029:794.690    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 029:794.709   Data:  32 F8 10 10 0B 0A 05 F8 10 30 05 EB 40 03 40 1C ...
T15FC 029:794.727 - 0.093ms returns 60 (0x3C)
T15FC 029:794.742 JLINK_ReadMemEx(0x08002218, 0x2 Bytes, Flags = 0x02000000)
T15FC 029:794.755    -- Read from flash cache (2 bytes @ 0x08002218)
T15FC 029:794.773   Data:  32 F8
T15FC 029:794.791 - 0.055ms returns 2 (0x2)
T15FC 029:794.807 JLINK_ReadMemEx(0x08002218, 0x3C Bytes, Flags = 0x02000000)
T15FC 029:794.828    -- Read from flash cache (60 bytes @ 0x08002218)
T15FC 029:794.856    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 029:794.874    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 029:794.893   Data:  32 F8 10 10 0B 0A 05 F8 10 30 05 EB 40 03 40 1C ...
T15FC 029:794.911 - 0.110ms returns 60 (0x3C)
T15FC 029:794.927 JLINK_ReadMemEx(0x08002218, 0x2 Bytes, Flags = 0x02000000)
T15FC 029:794.941    -- Read from flash cache (2 bytes @ 0x08002218)
T15FC 029:794.959   Data:  32 F8
T15FC 029:794.977 - 0.056ms returns 2 (0x2)
T15FC 029:794.992 JLINK_ReadMemEx(0x0800221A, 0x2 Bytes, Flags = 0x02000000)
T15FC 029:795.005    -- Read from flash cache (2 bytes @ 0x0800221A)
T15FC 029:795.023   Data:  10 10
T15FC 029:795.041 - 0.055ms returns 2 (0x2)
T15FC 029:795.057 JLINK_HasError()
T15FC 029:795.073 JLINK_Step()
T15FC 029:795.088    -- Read from flash cache (2 bytes @ 0x08002218)
T15FC 029:795.108   CPU_ReadMem(4 bytes @ 0xE0001004)
T15FC 029:796.063    -- Read from flash cache (2 bytes @ 0x0800221A)
T15FC 029:796.092   -- Not simulated
T15FC 029:801.272 - 6.219ms returns 0
T15FC 029:801.306 JLINK_HasError()
T15FC 029:801.324 JLINK_ReadReg(R15 (PC))
T15FC 029:801.339 - 0.022ms returns 0x0800221C
T15FC 029:801.355 JLINK_ReadReg(XPSR)
T15FC 029:801.368 - 0.019ms returns 0x41000000
T15FC 029:801.392 JLINK_ReadMemEx(0x0800221A, 0x2 Bytes, Flags = 0x02000000)
T15FC 029:801.407    -- Read from flash cache (2 bytes @ 0x0800221A)
T15FC 029:801.426   Data:  10 10
T15FC 029:801.445 - 0.058ms returns 2 (0x2)
T15FC 029:801.461 JLINK_ReadMemEx(0x0800221C, 0x3C Bytes, Flags = 0x02000000)
T15FC 029:801.474    -- Read from flash cache (60 bytes @ 0x0800221C)
T15FC 029:801.492    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 029:801.511    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 029:801.529   Data:  0B 0A 05 F8 10 30 05 EB 40 03 40 1C 59 70 08 28 ...
T15FC 029:801.547 - 0.092ms returns 60 (0x3C)
T15FC 029:801.562 JLINK_ReadMemEx(0x0800221C, 0x2 Bytes, Flags = 0x02000000)
T15FC 029:801.575    -- Read from flash cache (2 bytes @ 0x0800221C)
T15FC 029:801.593   Data:  0B 0A
T15FC 029:801.611 - 0.058ms returns 2 (0x2)
T15FC 029:801.632 JLINK_ReadMemEx(0x0800221C, 0x3C Bytes, Flags = 0x02000000)
T15FC 029:801.645    -- Read from flash cache (60 bytes @ 0x0800221C)
T15FC 029:801.664    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 029:801.682    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 029:801.700   Data:  0B 0A 05 F8 10 30 05 EB 40 03 40 1C 59 70 08 28 ...
T15FC 029:801.718 - 0.091ms returns 60 (0x3C)
T15FC 029:801.733 JLINK_ReadMemEx(0x0800221C, 0x2 Bytes, Flags = 0x02000000)
T15FC 029:801.746    -- Read from flash cache (2 bytes @ 0x0800221C)
T15FC 029:801.764   Data:  0B 0A
T15FC 029:801.782 - 0.055ms returns 2 (0x2)
T15FC 029:801.800 JLINK_ReadMemEx(0x0800221E, 0x2 Bytes, Flags = 0x02000000)
T15FC 029:801.826    -- Read from flash cache (2 bytes @ 0x0800221E)
T15FC 029:801.858   Data:  05 F8
T15FC 029:801.879 - 0.085ms returns 2 (0x2)
T15FC 029:801.896 JLINK_HasError()
T15FC 029:801.912 JLINK_Step()
T15FC 029:801.927    -- Read from flash cache (2 bytes @ 0x0800221C)
T15FC 029:801.947   CPU_ReadMem(4 bytes @ 0xE0001004)
T15FC 029:802.958   -- Simulated
T15FC 029:802.984 - 1.078ms returns 0
T15FC 029:803.003 JLINK_HasError()
T15FC 029:803.018 JLINK_ReadReg(R15 (PC))
T15FC 029:803.034 - 0.021ms returns 0x0800221E
T15FC 029:803.049 JLINK_ReadReg(XPSR)
T15FC 029:803.063 - 0.019ms returns 0x01000000
T15FC 029:803.082 JLINK_ReadMemEx(0x0800221E, 0x2 Bytes, Flags = 0x02000000)
T15FC 029:803.097    -- Read from flash cache (2 bytes @ 0x0800221E)
T15FC 029:803.115   Data:  05 F8
T15FC 029:803.134 - 0.058ms returns 2 (0x2)
T15FC 029:803.150 JLINK_ReadMemEx(0x08002220, 0x3C Bytes, Flags = 0x02000000)
T15FC 029:803.163    -- Read from flash cache (60 bytes @ 0x08002220)
T15FC 029:803.181    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 029:803.200    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 029:803.218    -- Merging zombie BP[4]: 0x5D28 @ 0x08002258
T15FC 029:803.236   Data:  10 30 05 EB 40 03 40 1C 59 70 08 28 F4 DB 10 22 ...
T15FC 029:803.254 - 0.110ms returns 60 (0x3C)
T15FC 029:803.269 JLINK_ReadMemEx(0x08002220, 0x2 Bytes, Flags = 0x02000000)
T15FC 029:803.282    -- Read from flash cache (2 bytes @ 0x08002220)
T15FC 029:803.300   Data:  10 30
T15FC 029:803.319 - 0.055ms returns 2 (0x2)
T15FC 029:803.334 JLINK_HasError()
T15FC 029:803.350 JLINK_Step()
T15FC 029:803.363    -- Read from flash cache (2 bytes @ 0x0800221E)
T15FC 029:803.383    -- Read from flash cache (2 bytes @ 0x08002220)
T15FC 029:803.401   -- Not simulated
T15FC 029:803.427   CPU_WriteMem(4 bytes @ 0xE0001004)
T15FC 029:810.867 - 7.539ms returns 0
T15FC 029:810.906 JLINK_HasError()
T15FC 029:810.924 JLINK_ReadReg(R15 (PC))
T15FC 029:810.940 - 0.022ms returns 0x08002222
T15FC 029:810.958 JLINK_ReadReg(XPSR)
T15FC 029:810.972 - 0.020ms returns 0x01000000
T15FC 029:811.097 JLINK_HasError()
T15FC 029:811.123 JLINK_ReadReg(R0)
T15FC 029:811.139 - 0.022ms returns 0x00000000
T15FC 029:811.156 JLINK_ReadReg(R1)
T15FC 029:811.169 - 0.019ms returns 0x00000301
T15FC 029:811.184 JLINK_ReadReg(R2)
T15FC 029:811.197 - 0.019ms returns 0x2000002C
T15FC 029:811.212 JLINK_ReadReg(R3)
T15FC 029:811.225 - 0.018ms returns 0x00000003
T15FC 029:811.240 JLINK_ReadReg(R4)
T15FC 029:811.253 - 0.019ms returns 0x2000000C
T15FC 029:811.268 JLINK_ReadReg(R5)
T15FC 029:811.280 - 0.019ms returns 0x20000558
T15FC 029:811.296 JLINK_ReadReg(R6)
T15FC 029:811.308 - 0.019ms returns 0x00000000
T15FC 029:811.323 JLINK_ReadReg(R7)
T15FC 029:811.336 - 0.018ms returns 0x00000000
T15FC 029:811.351 JLINK_ReadReg(R8)
T15FC 029:811.364 - 0.018ms returns 0x00000000
T15FC 029:811.381 JLINK_ReadReg(R9)
T15FC 029:811.393 - 0.019ms returns 0x00000000
T15FC 029:811.409 JLINK_ReadReg(R10)
T15FC 029:811.421 - 0.018ms returns 0x00000000
T15FC 029:811.436 JLINK_ReadReg(R11)
T15FC 029:811.449 - 0.018ms returns 0x00000000
T15FC 029:811.464 JLINK_ReadReg(R12)
T15FC 029:811.477 - 0.019ms returns 0x0000000E
T15FC 029:811.492 JLINK_ReadReg(R13 (SP))
T15FC 029:811.505 - 0.019ms returns 0x20000558
T15FC 029:811.523 JLINK_ReadReg(R14)
T15FC 029:811.537 - 0.020ms returns 0x080027FB
T15FC 029:811.553 JLINK_ReadReg(R15 (PC))
T15FC 029:811.565 - 0.019ms returns 0x08002222
T15FC 029:811.581 JLINK_ReadReg(XPSR)
T15FC 029:811.593 - 0.019ms returns 0x01000000
T15FC 029:811.609 JLINK_ReadReg(MSP)
T15FC 029:811.621 - 0.019ms returns 0x20000558
T15FC 029:811.636 JLINK_ReadReg(PSP)
T15FC 029:811.649 - 0.019ms returns 0x00000000
T15FC 029:811.664 JLINK_ReadReg(CFBP)
T15FC 029:811.677 - 0.018ms returns 0x00000000
T15FC 029:811.692 JLINK_ReadReg(FPSCR)
T15FC 029:817.665 - 5.987ms returns 0x00000000
T15FC 029:817.692 JLINK_ReadReg(FPS0)
T15FC 029:817.708 - 0.021ms returns 0x00000000
T15FC 029:817.733 JLINK_ReadReg(FPS1)
T15FC 029:817.752 - 0.025ms returns 0x00000000
T15FC 029:817.769 JLINK_ReadReg(FPS2)
T15FC 029:817.781 - 0.019ms returns 0x00000000
T15FC 029:817.797 JLINK_ReadReg(FPS3)
T15FC 029:817.809 - 0.019ms returns 0x00000000
T15FC 029:817.825 JLINK_ReadReg(FPS4)
T15FC 029:817.837 - 0.018ms returns 0x00000000
T15FC 029:817.852 JLINK_ReadReg(FPS5)
T15FC 029:817.865 - 0.018ms returns 0x00000000
T15FC 029:817.880 JLINK_ReadReg(FPS6)
T15FC 029:817.893 - 0.019ms returns 0x00000000
T15FC 029:817.908 JLINK_ReadReg(FPS7)
T15FC 029:817.921 - 0.018ms returns 0x00000000
T15FC 029:817.936 JLINK_ReadReg(FPS8)
T15FC 029:817.949 - 0.018ms returns 0x00000000
T15FC 029:817.964 JLINK_ReadReg(FPS9)
T15FC 029:817.976 - 0.019ms returns 0x00000000
T15FC 029:817.992 JLINK_ReadReg(FPS10)
T15FC 029:818.004 - 0.018ms returns 0x00000000
T15FC 029:818.019 JLINK_ReadReg(FPS11)
T15FC 029:818.032 - 0.018ms returns 0x00000000
T15FC 029:818.047 JLINK_ReadReg(FPS12)
T15FC 029:818.060 - 0.019ms returns 0x00000000
T15FC 029:818.075 JLINK_ReadReg(FPS13)
T15FC 029:818.088 - 0.018ms returns 0x00000000
T15FC 029:818.103 JLINK_ReadReg(FPS14)
T15FC 029:818.115 - 0.018ms returns 0x00000000
T15FC 029:818.131 JLINK_ReadReg(FPS15)
T15FC 029:818.143 - 0.019ms returns 0x00000000
T15FC 029:818.159 JLINK_ReadReg(FPS16)
T15FC 029:818.171 - 0.019ms returns 0x00000000
T15FC 029:818.186 JLINK_ReadReg(FPS17)
T15FC 029:818.199 - 0.018ms returns 0x00000000
T15FC 029:818.214 JLINK_ReadReg(FPS18)
T15FC 029:818.227 - 0.018ms returns 0x00000000
T15FC 029:818.242 JLINK_ReadReg(FPS19)
T15FC 029:818.254 - 0.018ms returns 0x00000000
T15FC 029:818.269 JLINK_ReadReg(FPS20)
T15FC 029:818.282 - 0.018ms returns 0x00000000
T15FC 029:818.297 JLINK_ReadReg(FPS21)
T15FC 029:818.310 - 0.018ms returns 0x00000000
T15FC 029:818.325 JLINK_ReadReg(FPS22)
T15FC 029:818.338 - 0.019ms returns 0x00000000
T15FC 029:818.353 JLINK_ReadReg(FPS23)
T15FC 029:818.366 - 0.018ms returns 0x00000000
T15FC 029:818.381 JLINK_ReadReg(FPS24)
T15FC 029:818.393 - 0.018ms returns 0x00000000
T15FC 029:818.408 JLINK_ReadReg(FPS25)
T15FC 029:818.421 - 0.018ms returns 0x00000000
T15FC 029:818.436 JLINK_ReadReg(FPS26)
T15FC 029:818.449 - 0.018ms returns 0x00000000
T15FC 029:818.464 JLINK_ReadReg(FPS27)
T15FC 029:818.477 - 0.018ms returns 0x00000000
T15FC 029:818.492 JLINK_ReadReg(FPS28)
T15FC 029:818.504 - 0.019ms returns 0x00000000
T15FC 029:818.520 JLINK_ReadReg(FPS29)
T15FC 029:818.532 - 0.018ms returns 0x00000000
T15FC 029:818.547 JLINK_ReadReg(FPS30)
T15FC 029:818.560 - 0.018ms returns 0x00000000
T15FC 029:818.575 JLINK_ReadReg(FPS31)
T15FC 029:818.588 - 0.018ms returns 0x00000000
T2BFC 029:818.801 JLINK_ReadMemEx(0x20000584, 0x4 Bytes, Flags = 0x02000000)
T2BFC 029:818.832   CPU_ReadMem(64 bytes @ 0x20000580)
T2BFC 029:820.474    -- Updating C cache (64 bytes @ 0x20000580)
T2BFC 029:820.501    -- Read from C cache (4 bytes @ 0x20000584)
T2BFC 029:820.520   Data:  FB 27 00 08
T2BFC 029:820.539 - 1.744ms returns 4 (0x4)
T2BFC 029:820.561 JLINK_ReadMemEx(0x20000578, 0x4 Bytes, Flags = 0x02000000)
T2BFC 029:820.579   CPU_ReadMem(64 bytes @ 0x20000540)
T2BFC 029:822.176    -- Updating C cache (64 bytes @ 0x20000540)
T2BFC 029:822.203    -- Read from C cache (4 bytes @ 0x20000578)
T2BFC 029:822.222   Data:  0C 00 00 20
T2BFC 029:822.241 - 1.688ms returns 4 (0x4)
T2BFC 029:822.262 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T2BFC 029:822.279    -- Read from C cache (4 bytes @ 0x2000057C)
T2BFC 029:822.298   Data:  5C 28 00 08
T2BFC 029:822.316 - 0.059ms returns 4 (0x4)
T2BFC 029:822.331 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T2BFC 029:822.346    -- Read from C cache (4 bytes @ 0x20000580)
T2BFC 029:822.364   Data:  00 00 00 00
T2BFC 029:822.382 - 0.056ms returns 4 (0x4)
T2BFC 029:822.397 JLINK_ReadMemEx(0x20000584, 0x4 Bytes, Flags = 0x02000000)
T2BFC 029:822.411    -- Read from C cache (4 bytes @ 0x20000584)
T2BFC 029:822.429   Data:  FB 27 00 08
T2BFC 029:822.447 - 0.056ms returns 4 (0x4)
T2BFC 029:822.479 JLINK_ReadMemEx(0x20000558, 0x10 Bytes, Flags = 0x02000000)
T2BFC 029:822.494    -- Read from C cache (16 bytes @ 0x20000558)
T2BFC 029:822.512   Data:  03 01 03 02 00 00 00 00 00 00 00 00 00 00 00 00
T2BFC 029:822.530 - 0.057ms returns 16 (0x10)
T2BFC 029:822.559 JLINK_ReadMemEx(0x20000568, 0x10 Bytes, Flags = 0x02000000)
T2BFC 029:822.573    -- Read from C cache (16 bytes @ 0x20000568)
T2BFC 029:822.592   Data:  00 00 00 00 00 00 00 00 00 00 00 00 01 00 00 00
T2BFC 029:822.610 - 0.057ms returns 16 (0x10)
T2BFC 029:822.646 JLINK_HasError()
T2BFC 029:822.662 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T2BFC 029:822.678   CPU_ReadMem(4 bytes @ 0xE0001004)
T2BFC 029:823.734   Data:  65 E2 CB 09
T2BFC 029:823.778   Debug reg: DWT_CYCCNT
T2BFC 029:823.798 - 1.141ms returns 1 (0x1)
T2BFC 029:825.445 JLINK_ReadMemEx(0x20000559, 0x1 Bytes, Flags = 0x02000000)
T2BFC 029:825.493    -- Read from C cache (1 bytes @ 0x20000559)
T2BFC 029:825.512   Data:  01
T2BFC 029:825.531 - 0.093ms returns 1 (0x1)
T2BFC 029:825.815 JLINK_ReadMemEx(0x20000559, 0x1 Bytes, Flags = 0x02000000)
T2BFC 029:825.839    -- Read from C cache (1 bytes @ 0x20000559)
T2BFC 029:825.859   Data:  01
T2BFC 029:825.878 - 0.069ms returns 1 (0x1)
T2BFC 029:827.298 JLINK_ReadMemEx(0x2000002C, 0x2 Bytes, Flags = 0x02000000)
T2BFC 029:827.327   CPU_ReadMem(64 bytes @ 0x20000000)
T2BFC 029:828.823    -- Updating C cache (64 bytes @ 0x20000000)
T2BFC 029:828.857    -- Read from C cache (2 bytes @ 0x2000002C)
T2BFC 029:828.876   Data:  01 03
T2BFC 029:828.895 - 1.603ms returns 2 (0x2)
T2BFC 029:828.919 JLINK_WriteMemEx(0x20000559, 0x00000001 Bytes, Flags = 0x02000000)
T2BFC 029:828.933   Data:  01
T2BFC 029:828.959   CPU_WriteMem(1 bytes @ 0x20000559)
T2BFC 029:829.840 - 0.935ms returns 0x1
T2BFC 029:830.192 JLINK_ReadMemEx(0x20000558, 0x1 Bytes, Flags = 0x02000000)
T2BFC 029:830.218   CPU_ReadMem(64 bytes @ 0x20000540)
T2BFC 029:831.749    -- Updating C cache (64 bytes @ 0x20000540)
T2BFC 029:831.782    -- Read from C cache (1 bytes @ 0x20000558)
T2BFC 029:831.801   Data:  03
T2BFC 029:831.819 - 1.633ms returns 1 (0x1)
T2BFC 029:833.334 JLINK_ReadMemEx(0x2000002C, 0x2 Bytes, Flags = 0x02000000)
T2BFC 029:833.360    -- Read from C cache (2 bytes @ 0x2000002C)
T2BFC 029:833.379   Data:  01 03
T2BFC 029:833.398 - 0.071ms returns 2 (0x2)
T2BFC 029:833.417 JLINK_WriteMemEx(0x20000558, 0x00000001 Bytes, Flags = 0x02000000)
T2BFC 029:833.431   Data:  03
T2BFC 029:833.456   CPU_WriteMem(1 bytes @ 0x20000558)
T2BFC 029:834.462 - 1.059ms returns 0x1
T15FC 030:446.819 JLINK_ReadMemEx(0x08002222, 0x2 Bytes, Flags = 0x02000000)
T15FC 030:446.860    -- Read from flash cache (2 bytes @ 0x08002222)
T15FC 030:446.880   Data:  05 EB
T15FC 030:446.900 - 0.087ms returns 2 (0x2)
T15FC 030:446.917 JLINK_ReadMemEx(0x08002224, 0x3C Bytes, Flags = 0x02000000)
T15FC 030:446.931    -- Read from flash cache (60 bytes @ 0x08002224)
T15FC 030:446.950    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 030:446.969    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 030:446.988    -- Merging zombie BP[4]: 0x5D28 @ 0x08002258
T15FC 030:447.007   Data:  40 03 40 1C 59 70 08 28 F4 DB 10 22 29 46 00 20 ...
T15FC 030:447.026 - 0.115ms returns 60 (0x3C)
T15FC 030:447.046 JLINK_ReadMemEx(0x08002224, 0x2 Bytes, Flags = 0x02000000)
T15FC 030:447.072    -- Read from flash cache (2 bytes @ 0x08002224)
T15FC 030:447.097   Data:  40 03
T15FC 030:447.115 - 0.075ms returns 2 (0x2)
T15FC 030:447.132 JLINK_ReadMemEx(0x08002222, 0x2 Bytes, Flags = 0x02000000)
T15FC 030:447.146    -- Read from flash cache (2 bytes @ 0x08002222)
T15FC 030:447.164   Data:  05 EB
T15FC 030:447.182 - 0.056ms returns 2 (0x2)
T15FC 030:447.198 JLINK_HasError()
T15FC 030:447.217 JLINK_Step()
T15FC 030:447.886    -- Read from flash cache (2 bytes @ 0x08002222)
T15FC 030:447.918    -- Read from flash cache (2 bytes @ 0x08002224)
T15FC 030:447.937   -- Not simulated
T15FC 030:452.840 - 5.643ms returns 0
T15FC 030:452.874 JLINK_HasError()
T15FC 030:452.891 JLINK_ReadReg(R15 (PC))
T15FC 030:452.907 - 0.022ms returns 0x08002226
T15FC 030:452.923 JLINK_ReadReg(XPSR)
T15FC 030:452.936 - 0.019ms returns 0x01000000
T15FC 030:452.961 JLINK_ReadMemEx(0x08002224, 0x3C Bytes, Flags = 0x02000000)
T15FC 030:452.976    -- Read from flash cache (60 bytes @ 0x08002224)
T15FC 030:452.995    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 030:453.013    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 030:453.031    -- Merging zombie BP[4]: 0x5D28 @ 0x08002258
T15FC 030:453.053   Data:  40 03 40 1C 59 70 08 28 F4 DB 10 22 29 46 00 20 ...
T15FC 030:453.078 - 0.126ms returns 60 (0x3C)
T15FC 030:453.105 JLINK_ReadMemEx(0x08002224, 0x2 Bytes, Flags = 0x02000000)
T15FC 030:453.126    -- Read from flash cache (2 bytes @ 0x08002224)
T15FC 030:453.153   Data:  40 03
T15FC 030:453.177 - 0.083ms returns 2 (0x2)
T15FC 030:453.204 JLINK_ReadMemEx(0x08002226, 0x2 Bytes, Flags = 0x02000000)
T15FC 030:453.229    -- Read from flash cache (2 bytes @ 0x08002226)
T15FC 030:453.262   Data:  40 1C
T15FC 030:453.293 - 0.096ms returns 2 (0x2)
T15FC 030:453.310 JLINK_ReadMemEx(0x08002226, 0x2 Bytes, Flags = 0x02000000)
T15FC 030:453.324    -- Read from flash cache (2 bytes @ 0x08002226)
T15FC 030:453.343   Data:  40 1C
T15FC 030:453.361 - 0.056ms returns 2 (0x2)
T15FC 030:453.376 JLINK_ReadMemEx(0x08002228, 0x3C Bytes, Flags = 0x02000000)
T15FC 030:453.389    -- Read from flash cache (60 bytes @ 0x08002228)
T15FC 030:453.408    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 030:453.426    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 030:453.444    -- Merging zombie BP[4]: 0x5D28 @ 0x08002258
T15FC 030:453.463   Data:  59 70 08 28 F4 DB 10 22 29 46 00 20 FE F7 59 F8 ...
T15FC 030:453.480 - 0.110ms returns 60 (0x3C)
T15FC 030:453.495 JLINK_ReadMemEx(0x08002228, 0x2 Bytes, Flags = 0x02000000)
T15FC 030:453.508    -- Read from flash cache (2 bytes @ 0x08002228)
T15FC 030:453.526   Data:  59 70
T15FC 030:453.544 - 0.055ms returns 2 (0x2)
T15FC 030:453.560 JLINK_HasError()
T15FC 030:453.576 JLINK_Step()
T15FC 030:453.591    -- Read from flash cache (2 bytes @ 0x08002226)
T15FC 030:453.611   CPU_ReadMem(4 bytes @ 0xE0001004)
T15FC 030:454.659   -- Simulated
T15FC 030:454.694 - 1.124ms returns 0
T15FC 030:454.712 JLINK_HasError()
T15FC 030:454.728 JLINK_ReadReg(R15 (PC))
T15FC 030:454.744 - 0.022ms returns 0x08002228
T15FC 030:454.760 JLINK_ReadReg(XPSR)
T15FC 030:454.773 - 0.019ms returns 0x01000000
T15FC 030:454.793 JLINK_ReadMemEx(0x08002228, 0x3C Bytes, Flags = 0x02000000)
T15FC 030:454.808    -- Read from flash cache (60 bytes @ 0x08002228)
T15FC 030:454.826    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 030:454.845    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 030:454.863    -- Merging zombie BP[4]: 0x5D28 @ 0x08002258
T15FC 030:454.882   Data:  59 70 08 28 F4 DB 10 22 29 46 00 20 FE F7 59 F8 ...
T15FC 030:454.899 - 0.112ms returns 60 (0x3C)
T15FC 030:454.915 JLINK_ReadMemEx(0x08002228, 0x2 Bytes, Flags = 0x02000000)
T15FC 030:454.928    -- Read from flash cache (2 bytes @ 0x08002228)
T15FC 030:454.946   Data:  59 70
T15FC 030:454.965 - 0.055ms returns 2 (0x2)
T15FC 030:454.980 JLINK_ReadMemEx(0x0800222A, 0x2 Bytes, Flags = 0x02000000)
T15FC 030:454.993    -- Read from flash cache (2 bytes @ 0x0800222A)
T15FC 030:455.011   Data:  08 28
T15FC 030:455.034 - 0.059ms returns 2 (0x2)
T15FC 030:455.053 JLINK_HasError()
T15FC 030:455.096 JLINK_Step()
T15FC 030:455.112    -- Read from flash cache (2 bytes @ 0x08002228)
T15FC 030:455.136   CPU_WriteMem(1 bytes @ 0x20000559)
T15FC 030:456.227   -- Simulated
T15FC 030:456.254 - 1.164ms returns 0
T15FC 030:456.273 JLINK_HasError()
T15FC 030:456.289 JLINK_ReadReg(R15 (PC))
T15FC 030:456.304 - 0.021ms returns 0x0800222A
T15FC 030:456.320 JLINK_ReadReg(XPSR)
T15FC 030:456.333 - 0.019ms returns 0x01000000
T15FC 030:456.567 JLINK_HasError()
T15FC 030:456.592 JLINK_ReadReg(R0)
T15FC 030:456.608 - 0.022ms returns 0x00000001
T15FC 030:456.624 JLINK_ReadReg(R1)
T15FC 030:456.638 - 0.019ms returns 0x00000301
T15FC 030:456.653 JLINK_ReadReg(R2)
T15FC 030:456.666 - 0.019ms returns 0x2000002C
T15FC 030:456.681 JLINK_ReadReg(R3)
T15FC 030:456.694 - 0.018ms returns 0x20000558
T15FC 030:456.709 JLINK_ReadReg(R4)
T15FC 030:456.722 - 0.019ms returns 0x2000000C
T15FC 030:456.737 JLINK_ReadReg(R5)
T15FC 030:456.749 - 0.018ms returns 0x20000558
T15FC 030:456.765 JLINK_ReadReg(R6)
T15FC 030:456.777 - 0.019ms returns 0x00000000
T15FC 030:456.792 JLINK_ReadReg(R7)
T15FC 030:456.805 - 0.019ms returns 0x00000000
T15FC 030:456.820 JLINK_ReadReg(R8)
T15FC 030:456.833 - 0.019ms returns 0x00000000
T15FC 030:456.848 JLINK_ReadReg(R9)
T15FC 030:456.861 - 0.018ms returns 0x00000000
T15FC 030:456.876 JLINK_ReadReg(R10)
T15FC 030:456.888 - 0.018ms returns 0x00000000
T15FC 030:456.904 JLINK_ReadReg(R11)
T15FC 030:456.916 - 0.018ms returns 0x00000000
T15FC 030:456.931 JLINK_ReadReg(R12)
T15FC 030:456.944 - 0.018ms returns 0x0000000E
T15FC 030:456.959 JLINK_ReadReg(R13 (SP))
T15FC 030:456.972 - 0.019ms returns 0x20000558
T15FC 030:456.987 JLINK_ReadReg(R14)
T15FC 030:456.999 - 0.018ms returns 0x080027FB
T15FC 030:457.015 JLINK_ReadReg(R15 (PC))
T15FC 030:457.029 - 0.020ms returns 0x0800222A
T15FC 030:457.046 JLINK_ReadReg(XPSR)
T15FC 030:457.069 - 0.036ms returns 0x01000000
T15FC 030:457.097 JLINK_ReadReg(MSP)
T15FC 030:457.112 - 0.021ms returns 0x20000558
T15FC 030:457.127 JLINK_ReadReg(PSP)
T15FC 030:457.140 - 0.019ms returns 0x00000000
T15FC 030:457.157 JLINK_ReadReg(CFBP)
T15FC 030:457.170 - 0.019ms returns 0x00000000
T15FC 030:457.185 JLINK_ReadReg(FPSCR)
T15FC 030:463.008 - 5.855ms returns 0x00000000
T15FC 030:463.060 JLINK_ReadReg(FPS0)
T15FC 030:463.077 - 0.024ms returns 0x00000000
T15FC 030:463.093 JLINK_ReadReg(FPS1)
T15FC 030:463.106 - 0.019ms returns 0x00000000
T15FC 030:463.123 JLINK_ReadReg(FPS2)
T15FC 030:463.136 - 0.019ms returns 0x00000000
T15FC 030:463.151 JLINK_ReadReg(FPS3)
T15FC 030:463.164 - 0.019ms returns 0x00000000
T15FC 030:463.181 JLINK_ReadReg(FPS4)
T15FC 030:463.194 - 0.019ms returns 0x00000000
T15FC 030:463.211 JLINK_ReadReg(FPS5)
T15FC 030:463.223 - 0.019ms returns 0x00000000
T15FC 030:463.238 JLINK_ReadReg(FPS6)
T15FC 030:463.251 - 0.019ms returns 0x00000000
T15FC 030:463.268 JLINK_ReadReg(FPS7)
T15FC 030:463.281 - 0.019ms returns 0x00000000
T15FC 030:463.298 JLINK_ReadReg(FPS8)
T15FC 030:463.311 - 0.019ms returns 0x00000000
T15FC 030:463.326 JLINK_ReadReg(FPS9)
T15FC 030:463.339 - 0.018ms returns 0x00000000
T15FC 030:463.355 JLINK_ReadReg(FPS10)
T15FC 030:463.368 - 0.019ms returns 0x00000000
T15FC 030:463.385 JLINK_ReadReg(FPS11)
T15FC 030:463.398 - 0.019ms returns 0x00000000
T15FC 030:463.413 JLINK_ReadReg(FPS12)
T15FC 030:463.425 - 0.018ms returns 0x00000000
T15FC 030:463.442 JLINK_ReadReg(FPS13)
T15FC 030:463.455 - 0.019ms returns 0x00000000
T15FC 030:463.472 JLINK_ReadReg(FPS14)
T15FC 030:463.485 - 0.018ms returns 0x00000000
T15FC 030:463.500 JLINK_ReadReg(FPS15)
T15FC 030:463.512 - 0.018ms returns 0x00000000
T15FC 030:463.530 JLINK_ReadReg(FPS16)
T15FC 030:463.542 - 0.019ms returns 0x00000000
T15FC 030:463.559 JLINK_ReadReg(FPS17)
T15FC 030:463.572 - 0.018ms returns 0x00000000
T15FC 030:463.587 JLINK_ReadReg(FPS18)
T15FC 030:463.600 - 0.019ms returns 0x00000000
T15FC 030:463.616 JLINK_ReadReg(FPS19)
T15FC 030:463.629 - 0.022ms returns 0x00000000
T15FC 030:463.651 JLINK_ReadReg(FPS20)
T15FC 030:463.664 - 0.019ms returns 0x00000000
T15FC 030:463.679 JLINK_ReadReg(FPS21)
T15FC 030:463.692 - 0.019ms returns 0x00000000
T15FC 030:463.709 JLINK_ReadReg(FPS22)
T15FC 030:463.721 - 0.019ms returns 0x00000000
T15FC 030:463.737 JLINK_ReadReg(FPS23)
T15FC 030:463.749 - 0.018ms returns 0x00000000
T15FC 030:463.766 JLINK_ReadReg(FPS24)
T15FC 030:463.779 - 0.019ms returns 0x00000000
T15FC 030:463.796 JLINK_ReadReg(FPS25)
T15FC 030:463.808 - 0.019ms returns 0x00000000
T15FC 030:463.824 JLINK_ReadReg(FPS26)
T15FC 030:463.836 - 0.018ms returns 0x00000000
T15FC 030:463.853 JLINK_ReadReg(FPS27)
T15FC 030:463.866 - 0.019ms returns 0x00000000
T15FC 030:463.883 JLINK_ReadReg(FPS28)
T15FC 030:463.896 - 0.019ms returns 0x00000000
T15FC 030:463.913 JLINK_ReadReg(FPS29)
T15FC 030:463.926 - 0.019ms returns 0x00000000
T15FC 030:463.943 JLINK_ReadReg(FPS30)
T15FC 030:463.956 - 0.018ms returns 0x00000000
T15FC 030:463.973 JLINK_ReadReg(FPS31)
T15FC 030:463.985 - 0.019ms returns 0x00000000
T2BFC 030:464.142 JLINK_ReadMemEx(0x20000584, 0x4 Bytes, Flags = 0x02000000)
T2BFC 030:464.176   CPU_ReadMem(64 bytes @ 0x20000580)
T2BFC 030:465.811    -- Updating C cache (64 bytes @ 0x20000580)
T2BFC 030:465.838    -- Read from C cache (4 bytes @ 0x20000584)
T2BFC 030:465.857   Data:  FB 27 00 08
T2BFC 030:465.876 - 1.740ms returns 4 (0x4)
T2BFC 030:465.898 JLINK_ReadMemEx(0x20000578, 0x4 Bytes, Flags = 0x02000000)
T2BFC 030:465.916   CPU_ReadMem(64 bytes @ 0x20000540)
T2BFC 030:467.517    -- Updating C cache (64 bytes @ 0x20000540)
T2BFC 030:467.544    -- Read from C cache (4 bytes @ 0x20000578)
T2BFC 030:467.563   Data:  0C 00 00 20
T2BFC 030:467.581 - 1.689ms returns 4 (0x4)
T2BFC 030:467.600 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T2BFC 030:467.617    -- Read from C cache (4 bytes @ 0x2000057C)
T2BFC 030:467.635   Data:  5C 28 00 08
T2BFC 030:467.653 - 0.059ms returns 4 (0x4)
T2BFC 030:467.669 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T2BFC 030:467.683    -- Read from C cache (4 bytes @ 0x20000580)
T2BFC 030:467.701   Data:  00 00 00 00
T2BFC 030:467.719 - 0.056ms returns 4 (0x4)
T2BFC 030:467.734 JLINK_ReadMemEx(0x20000584, 0x4 Bytes, Flags = 0x02000000)
T2BFC 030:467.748    -- Read from C cache (4 bytes @ 0x20000584)
T2BFC 030:467.766   Data:  FB 27 00 08
T2BFC 030:467.784 - 0.056ms returns 4 (0x4)
T2BFC 030:467.816 JLINK_ReadMemEx(0x20000558, 0x10 Bytes, Flags = 0x02000000)
T2BFC 030:467.831    -- Read from C cache (16 bytes @ 0x20000558)
T2BFC 030:467.850   Data:  03 01 03 02 00 00 00 00 00 00 00 00 00 00 00 00
T2BFC 030:467.868 - 0.058ms returns 16 (0x10)
T2BFC 030:467.887 JLINK_ReadMemEx(0x20000568, 0x10 Bytes, Flags = 0x02000000)
T2BFC 030:467.901    -- Read from C cache (16 bytes @ 0x20000568)
T2BFC 030:467.919   Data:  00 00 00 00 00 00 00 00 00 00 00 00 01 00 00 00
T2BFC 030:467.937 - 0.056ms returns 16 (0x10)
T2BFC 030:467.983 JLINK_HasError()
T2BFC 030:468.000 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T2BFC 030:468.018   Data:  69 E2 CB 09
T2BFC 030:468.037   Debug reg: DWT_CYCCNT
T2BFC 030:468.054 - 0.060ms returns 1 (0x1)
T2BFC 030:469.622 JLINK_ReadMemEx(0x2000055B, 0x1 Bytes, Flags = 0x02000000)
T2BFC 030:469.650    -- Read from C cache (1 bytes @ 0x2000055B)
T2BFC 030:469.669   Data:  02
T2BFC 030:469.688 - 0.072ms returns 1 (0x1)
T2BFC 030:469.937 JLINK_ReadMemEx(0x2000055B, 0x1 Bytes, Flags = 0x02000000)
T2BFC 030:469.958    -- Read from C cache (1 bytes @ 0x2000055B)
T2BFC 030:469.977   Data:  02
T2BFC 030:469.995 - 0.064ms returns 1 (0x1)
T2BFC 030:471.394 JLINK_ReadMemEx(0x2000002E, 0x2 Bytes, Flags = 0x02000000)
T2BFC 030:471.423   CPU_ReadMem(64 bytes @ 0x20000000)
T2BFC 030:473.009    -- Updating C cache (64 bytes @ 0x20000000)
T2BFC 030:473.037    -- Read from C cache (2 bytes @ 0x2000002E)
T2BFC 030:473.057   Data:  02 03
T2BFC 030:473.076 - 1.688ms returns 2 (0x2)
T2BFC 030:473.100 JLINK_WriteMemEx(0x2000055B, 0x00000001 Bytes, Flags = 0x02000000)
T2BFC 030:473.125   Data:  02
T2BFC 030:473.152   CPU_WriteMem(1 bytes @ 0x2000055B)
T2BFC 030:474.085 - 1.000ms returns 0x1
T2BFC 030:474.420 JLINK_ReadMemEx(0x2000055A, 0x1 Bytes, Flags = 0x02000000)
T2BFC 030:474.445   CPU_ReadMem(64 bytes @ 0x20000540)
T2BFC 030:475.914    -- Updating C cache (64 bytes @ 0x20000540)
T2BFC 030:475.950    -- Read from C cache (1 bytes @ 0x2000055A)
T2BFC 030:475.975   Data:  03
T2BFC 030:475.996 - 1.582ms returns 1 (0x1)
T2BFC 030:477.299 JLINK_ReadMemEx(0x2000002E, 0x2 Bytes, Flags = 0x02000000)
T2BFC 030:477.326    -- Read from C cache (2 bytes @ 0x2000002E)
T2BFC 030:477.345   Data:  02 03
T2BFC 030:477.364 - 0.071ms returns 2 (0x2)
T2BFC 030:477.383 JLINK_WriteMemEx(0x2000055A, 0x00000001 Bytes, Flags = 0x02000000)
T2BFC 030:477.396   Data:  03
T2BFC 030:477.422   CPU_WriteMem(1 bytes @ 0x2000055A)
T2BFC 030:478.514 - 1.146ms returns 0x1
T15FC 031:503.070 JLINK_ReadMemEx(0x0800222A, 0x2 Bytes, Flags = 0x02000000)
T15FC 031:503.111    -- Read from flash cache (2 bytes @ 0x0800222A)
T15FC 031:503.131   Data:  08 28
T15FC 031:503.151 - 0.087ms returns 2 (0x2)
T15FC 031:503.168 JLINK_ReadMemEx(0x0800222C, 0x3C Bytes, Flags = 0x02000000)
T15FC 031:503.183    -- Read from flash cache (60 bytes @ 0x0800222C)
T15FC 031:503.202    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 031:503.232    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 031:503.256    -- Merging zombie BP[4]: 0x5D28 @ 0x08002258
T15FC 031:503.283   Data:  F4 DB 10 22 29 46 00 20 FE F7 59 F8 0A 20 FE F7 ...
T15FC 031:503.302 - 0.139ms returns 60 (0x3C)
T15FC 031:503.318 JLINK_ReadMemEx(0x0800222C, 0x2 Bytes, Flags = 0x02000000)
T15FC 031:503.333    -- Read from flash cache (2 bytes @ 0x0800222C)
T15FC 031:503.351   Data:  F4 DB
T15FC 031:503.369 - 0.057ms returns 2 (0x2)
T15FC 031:503.386 JLINK_ReadMemEx(0x0800222A, 0x2 Bytes, Flags = 0x02000000)
T15FC 031:503.399    -- Read from flash cache (2 bytes @ 0x0800222A)
T15FC 031:503.417   Data:  08 28
T15FC 031:503.435 - 0.055ms returns 2 (0x2)
T15FC 031:503.451 JLINK_HasError()
T15FC 031:503.469 JLINK_Step()
T15FC 031:504.136    -- Read from flash cache (2 bytes @ 0x0800222A)
T15FC 031:504.169   -- Simulated
T15FC 031:504.189 - 0.726ms returns 0
T15FC 031:504.210 JLINK_HasError()
T15FC 031:504.239 JLINK_ReadReg(R15 (PC))
T15FC 031:504.262 - 0.029ms returns 0x0800222C
T15FC 031:504.279 JLINK_ReadReg(XPSR)
T15FC 031:504.292 - 0.019ms returns 0x81000000
T15FC 031:504.316 JLINK_ReadMemEx(0x0800222C, 0x3C Bytes, Flags = 0x02000000)
T15FC 031:504.331    -- Read from flash cache (60 bytes @ 0x0800222C)
T15FC 031:504.349    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 031:504.368    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 031:504.386    -- Merging zombie BP[4]: 0x5D28 @ 0x08002258
T15FC 031:504.405   Data:  F4 DB 10 22 29 46 00 20 FE F7 59 F8 0A 20 FE F7 ...
T15FC 031:504.423 - 0.113ms returns 60 (0x3C)
T15FC 031:504.438 JLINK_ReadMemEx(0x0800222C, 0x2 Bytes, Flags = 0x02000000)
T15FC 031:504.452    -- Read from flash cache (2 bytes @ 0x0800222C)
T15FC 031:504.472   Data:  F4 DB
T15FC 031:504.490 - 0.057ms returns 2 (0x2)
T15FC 031:504.506 JLINK_ReadMemEx(0x0800222E, 0x2 Bytes, Flags = 0x02000000)
T15FC 031:504.519    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 031:504.537    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 031:504.555   Data:  10 22
T15FC 031:504.573 - 0.074ms returns 2 (0x2)
T15FC 031:504.589 JLINK_HasError()
T15FC 031:504.605 JLINK_Step()
T15FC 031:504.619    -- Read from flash cache (2 bytes @ 0x0800222C)
T15FC 031:504.638   -- Simulated
T15FC 031:504.656 - 0.057ms returns 0
T15FC 031:504.671 JLINK_HasError()
T15FC 031:504.687 JLINK_ReadReg(R15 (PC))
T15FC 031:504.700 - 0.019ms returns 0x08002218
T15FC 031:504.715 JLINK_ReadReg(XPSR)
T15FC 031:504.728 - 0.019ms returns 0x81000000
T15FC 031:504.745 JLINK_ReadMemEx(0x08002216, 0x2 Bytes, Flags = 0x02000000)
T15FC 031:504.759    -- Read from flash cache (2 bytes @ 0x08002216)
T15FC 031:504.777   Data:  0D 05
T15FC 031:504.852 - 0.113ms returns 2 (0x2)
T15FC 031:504.869 JLINK_ReadMemEx(0x08002218, 0x3C Bytes, Flags = 0x02000000)
T15FC 031:504.883    -- Read from flash cache (60 bytes @ 0x08002218)
T15FC 031:504.935    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 031:504.953    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 031:504.972   Data:  32 F8 10 10 0B 0A 05 F8 10 30 05 EB 40 03 40 1C ...
T15FC 031:504.990 - 0.129ms returns 60 (0x3C)
T15FC 031:505.007 JLINK_ReadMemEx(0x08002218, 0x2 Bytes, Flags = 0x02000000)
T15FC 031:505.021    -- Read from flash cache (2 bytes @ 0x08002218)
T15FC 031:505.039   Data:  32 F8
T15FC 031:505.057 - 0.055ms returns 2 (0x2)
T15FC 031:505.073 JLINK_ReadMemEx(0x08002218, 0x3C Bytes, Flags = 0x02000000)
T15FC 031:505.086    -- Read from flash cache (60 bytes @ 0x08002218)
T15FC 031:505.104    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 031:505.123    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 031:505.141   Data:  32 F8 10 10 0B 0A 05 F8 10 30 05 EB 40 03 40 1C ...
T15FC 031:505.159 - 0.092ms returns 60 (0x3C)
T15FC 031:505.174 JLINK_ReadMemEx(0x08002218, 0x2 Bytes, Flags = 0x02000000)
T15FC 031:505.187    -- Read from flash cache (2 bytes @ 0x08002218)
T15FC 031:505.205   Data:  32 F8
T15FC 031:505.252 - 0.087ms returns 2 (0x2)
T15FC 031:505.279 JLINK_ReadMemEx(0x0800221A, 0x2 Bytes, Flags = 0x02000000)
T15FC 031:505.293    -- Read from flash cache (2 bytes @ 0x0800221A)
T15FC 031:505.312   Data:  10 10
T15FC 031:505.330 - 0.057ms returns 2 (0x2)
T15FC 031:505.346 JLINK_HasError()
T15FC 031:505.363 JLINK_Step()
T15FC 031:505.377    -- Read from flash cache (2 bytes @ 0x08002218)
T15FC 031:505.397    -- Read from flash cache (2 bytes @ 0x0800221A)
T15FC 031:505.415   -- Not simulated
T15FC 031:505.446   CPU_WriteMem(4 bytes @ 0xE0001004)
T15FC 031:512.782 - 7.446ms returns 0
T15FC 031:512.824 JLINK_HasError()
T15FC 031:512.841 JLINK_ReadReg(R15 (PC))
T15FC 031:512.857 - 0.022ms returns 0x0800221C
T15FC 031:512.873 JLINK_ReadReg(XPSR)
T15FC 031:512.886 - 0.019ms returns 0x81000000
T15FC 031:512.912 JLINK_ReadMemEx(0x0800221A, 0x2 Bytes, Flags = 0x02000000)
T15FC 031:512.928    -- Read from flash cache (2 bytes @ 0x0800221A)
T15FC 031:512.947   Data:  10 10
T15FC 031:512.965 - 0.059ms returns 2 (0x2)
T15FC 031:512.981 JLINK_ReadMemEx(0x0800221C, 0x3C Bytes, Flags = 0x02000000)
T15FC 031:512.995    -- Read from flash cache (60 bytes @ 0x0800221C)
T15FC 031:513.013    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 031:513.032    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 031:513.050   Data:  0B 0A 05 F8 10 30 05 EB 40 03 40 1C 59 70 08 28 ...
T15FC 031:513.068 - 0.092ms returns 60 (0x3C)
T15FC 031:513.083 JLINK_ReadMemEx(0x0800221C, 0x2 Bytes, Flags = 0x02000000)
T15FC 031:513.097    -- Read from flash cache (2 bytes @ 0x0800221C)
T15FC 031:513.115   Data:  0B 0A
T15FC 031:513.133 - 0.055ms returns 2 (0x2)
T15FC 031:513.148 JLINK_ReadMemEx(0x0800221C, 0x3C Bytes, Flags = 0x02000000)
T15FC 031:513.162    -- Read from flash cache (60 bytes @ 0x0800221C)
T15FC 031:513.180    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 031:513.198    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 031:513.219   Data:  0B 0A 05 F8 10 30 05 EB 40 03 40 1C 59 70 08 28 ...
T15FC 031:513.251 - 0.110ms returns 60 (0x3C)
T15FC 031:513.269 JLINK_ReadMemEx(0x0800221C, 0x2 Bytes, Flags = 0x02000000)
T15FC 031:513.282    -- Read from flash cache (2 bytes @ 0x0800221C)
T15FC 031:513.301   Data:  0B 0A
T15FC 031:513.319 - 0.056ms returns 2 (0x2)
T15FC 031:513.334 JLINK_ReadMemEx(0x0800221E, 0x2 Bytes, Flags = 0x02000000)
T15FC 031:513.348    -- Read from flash cache (2 bytes @ 0x0800221E)
T15FC 031:513.365   Data:  05 F8
T15FC 031:513.384 - 0.055ms returns 2 (0x2)
T15FC 031:513.399 JLINK_HasError()
T15FC 031:513.415 JLINK_Step()
T15FC 031:513.430    -- Read from flash cache (2 bytes @ 0x0800221C)
T15FC 031:513.450   CPU_ReadMem(4 bytes @ 0xE0001004)
T15FC 031:514.446   -- Simulated
T15FC 031:514.472 - 1.063ms returns 0
T15FC 031:514.494 JLINK_HasError()
T15FC 031:514.514 JLINK_ReadReg(R15 (PC))
T15FC 031:514.529 - 0.021ms returns 0x0800221E
T15FC 031:514.545 JLINK_ReadReg(XPSR)
T15FC 031:514.558 - 0.019ms returns 0x01000000
T15FC 031:514.577 JLINK_ReadMemEx(0x0800221E, 0x2 Bytes, Flags = 0x02000000)
T15FC 031:514.592    -- Read from flash cache (2 bytes @ 0x0800221E)
T15FC 031:514.611   Data:  05 F8
T15FC 031:514.629 - 0.057ms returns 2 (0x2)
T15FC 031:514.645 JLINK_ReadMemEx(0x08002220, 0x3C Bytes, Flags = 0x02000000)
T15FC 031:514.658    -- Read from flash cache (60 bytes @ 0x08002220)
T15FC 031:514.676    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 031:514.695    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 031:514.713    -- Merging zombie BP[4]: 0x5D28 @ 0x08002258
T15FC 031:514.731   Data:  10 30 05 EB 40 03 40 1C 59 70 08 28 F4 DB 10 22 ...
T15FC 031:514.749 - 0.110ms returns 60 (0x3C)
T15FC 031:514.764 JLINK_ReadMemEx(0x08002220, 0x2 Bytes, Flags = 0x02000000)
T15FC 031:514.777    -- Read from flash cache (2 bytes @ 0x08002220)
T15FC 031:514.795   Data:  10 30
T15FC 031:514.813 - 0.055ms returns 2 (0x2)
T15FC 031:514.829 JLINK_HasError()
T15FC 031:514.844 JLINK_Step()
T15FC 031:514.858    -- Read from flash cache (2 bytes @ 0x0800221E)
T15FC 031:514.877    -- Read from flash cache (2 bytes @ 0x08002220)
T15FC 031:514.895   -- Not simulated
T15FC 031:514.923   CPU_WriteMem(4 bytes @ 0xE0001004)
T15FC 031:522.246 - 7.426ms returns 0
T15FC 031:522.285 JLINK_HasError()
T15FC 031:522.302 JLINK_ReadReg(R15 (PC))
T15FC 031:522.318 - 0.022ms returns 0x08002222
T15FC 031:522.333 JLINK_ReadReg(XPSR)
T15FC 031:522.346 - 0.019ms returns 0x01000000
T15FC 031:522.595 JLINK_HasError()
T15FC 031:522.621 JLINK_ReadReg(R0)
T15FC 031:522.637 - 0.022ms returns 0x00000001
T15FC 031:522.654 JLINK_ReadReg(R1)
T15FC 031:522.667 - 0.019ms returns 0x00000302
T15FC 031:522.683 JLINK_ReadReg(R2)
T15FC 031:522.696 - 0.019ms returns 0x2000002C
T15FC 031:522.711 JLINK_ReadReg(R3)
T15FC 031:522.724 - 0.019ms returns 0x00000003
T15FC 031:522.739 JLINK_ReadReg(R4)
T15FC 031:522.752 - 0.019ms returns 0x2000000C
T15FC 031:522.767 JLINK_ReadReg(R5)
T15FC 031:522.780 - 0.019ms returns 0x20000558
T15FC 031:522.795 JLINK_ReadReg(R6)
T15FC 031:522.808 - 0.019ms returns 0x00000000
T15FC 031:522.823 JLINK_ReadReg(R7)
T15FC 031:522.835 - 0.018ms returns 0x00000000
T15FC 031:522.851 JLINK_ReadReg(R8)
T15FC 031:522.863 - 0.019ms returns 0x00000000
T15FC 031:522.879 JLINK_ReadReg(R9)
T15FC 031:522.891 - 0.019ms returns 0x00000000
T15FC 031:522.907 JLINK_ReadReg(R10)
T15FC 031:522.919 - 0.019ms returns 0x00000000
T15FC 031:522.935 JLINK_ReadReg(R11)
T15FC 031:522.947 - 0.019ms returns 0x00000000
T15FC 031:522.963 JLINK_ReadReg(R12)
T15FC 031:522.975 - 0.019ms returns 0x0000000E
T15FC 031:522.990 JLINK_ReadReg(R13 (SP))
T15FC 031:523.003 - 0.019ms returns 0x20000558
T15FC 031:523.019 JLINK_ReadReg(R14)
T15FC 031:523.031 - 0.019ms returns 0x080027FB
T15FC 031:523.047 JLINK_ReadReg(R15 (PC))
T15FC 031:523.059 - 0.019ms returns 0x08002222
T15FC 031:523.075 JLINK_ReadReg(XPSR)
T15FC 031:523.087 - 0.019ms returns 0x01000000
T15FC 031:523.103 JLINK_ReadReg(MSP)
T15FC 031:523.115 - 0.019ms returns 0x20000558
T15FC 031:523.130 JLINK_ReadReg(PSP)
T15FC 031:523.143 - 0.019ms returns 0x00000000
T15FC 031:523.163 JLINK_ReadReg(CFBP)
T15FC 031:523.176 - 0.020ms returns 0x00000000
T15FC 031:523.192 JLINK_ReadReg(FPSCR)
T15FC 031:529.209 - 6.041ms returns 0x00000000
T15FC 031:529.251 JLINK_ReadReg(FPS0)
T15FC 031:529.268 - 0.023ms returns 0x00000000
T15FC 031:529.284 JLINK_ReadReg(FPS1)
T15FC 031:529.297 - 0.019ms returns 0x00000000
T15FC 031:529.314 JLINK_ReadReg(FPS2)
T15FC 031:529.327 - 0.019ms returns 0x00000000
T15FC 031:529.343 JLINK_ReadReg(FPS3)
T15FC 031:529.356 - 0.019ms returns 0x00000000
T15FC 031:529.372 JLINK_ReadReg(FPS4)
T15FC 031:529.386 - 0.019ms returns 0x00000000
T15FC 031:529.401 JLINK_ReadReg(FPS5)
T15FC 031:529.413 - 0.019ms returns 0x00000000
T15FC 031:529.431 JLINK_ReadReg(FPS6)
T15FC 031:529.451 - 0.026ms returns 0x00000000
T15FC 031:529.468 JLINK_ReadReg(FPS7)
T15FC 031:529.481 - 0.019ms returns 0x00000000
T15FC 031:529.496 JLINK_ReadReg(FPS8)
T15FC 031:529.509 - 0.019ms returns 0x00000000
T15FC 031:529.526 JLINK_ReadReg(FPS9)
T15FC 031:529.538 - 0.019ms returns 0x00000000
T15FC 031:529.554 JLINK_ReadReg(FPS10)
T15FC 031:529.566 - 0.019ms returns 0x00000000
T15FC 031:529.584 JLINK_ReadReg(FPS11)
T15FC 031:529.596 - 0.019ms returns 0x00000000
T15FC 031:529.613 JLINK_ReadReg(FPS12)
T15FC 031:529.626 - 0.019ms returns 0x00000000
T15FC 031:529.641 JLINK_ReadReg(FPS13)
T15FC 031:529.654 - 0.019ms returns 0x00000000
T15FC 031:529.671 JLINK_ReadReg(FPS14)
T15FC 031:529.684 - 0.021ms returns 0x00000000
T15FC 031:529.718 JLINK_ReadReg(FPS15)
T15FC 031:529.731 - 0.019ms returns 0x00000000
T15FC 031:529.748 JLINK_ReadReg(FPS16)
T15FC 031:529.762 - 0.020ms returns 0x00000000
T15FC 031:529.778 JLINK_ReadReg(FPS17)
T15FC 031:529.790 - 0.019ms returns 0x00000000
T15FC 031:529.807 JLINK_ReadReg(FPS18)
T15FC 031:529.820 - 0.019ms returns 0x00000000
T15FC 031:529.835 JLINK_ReadReg(FPS19)
T15FC 031:529.848 - 0.019ms returns 0x00000000
T15FC 031:529.865 JLINK_ReadReg(FPS20)
T15FC 031:529.878 - 0.019ms returns 0x00000000
T15FC 031:529.894 JLINK_ReadReg(FPS21)
T15FC 031:529.907 - 0.019ms returns 0x00000000
T15FC 031:529.922 JLINK_ReadReg(FPS22)
T15FC 031:529.935 - 0.019ms returns 0x00000000
T15FC 031:529.952 JLINK_ReadReg(FPS23)
T15FC 031:529.965 - 0.019ms returns 0x00000000
T15FC 031:529.980 JLINK_ReadReg(FPS24)
T15FC 031:529.993 - 0.019ms returns 0x00000000
T15FC 031:530.011 JLINK_ReadReg(FPS25)
T15FC 031:530.024 - 0.019ms returns 0x00000000
T15FC 031:530.041 JLINK_ReadReg(FPS26)
T15FC 031:530.054 - 0.019ms returns 0x00000000
T15FC 031:530.071 JLINK_ReadReg(FPS27)
T15FC 031:530.084 - 0.019ms returns 0x00000000
T15FC 031:530.101 JLINK_ReadReg(FPS28)
T15FC 031:530.113 - 0.019ms returns 0x00000000
T15FC 031:530.131 JLINK_ReadReg(FPS29)
T15FC 031:530.151 - 0.033ms returns 0x00000000
T15FC 031:530.177 JLINK_ReadReg(FPS30)
T15FC 031:530.191 - 0.021ms returns 0x00000000
T15FC 031:530.207 JLINK_ReadReg(FPS31)
T15FC 031:530.220 - 0.019ms returns 0x00000000
T2BFC 031:530.368 JLINK_ReadMemEx(0x20000584, 0x4 Bytes, Flags = 0x02000000)
T2BFC 031:530.407   CPU_ReadMem(64 bytes @ 0x20000580)
T2BFC 031:532.039    -- Updating C cache (64 bytes @ 0x20000580)
T2BFC 031:532.067    -- Read from C cache (4 bytes @ 0x20000584)
T2BFC 031:532.087   Data:  FB 27 00 08
T2BFC 031:532.105 - 1.743ms returns 4 (0x4)
T2BFC 031:532.127 JLINK_ReadMemEx(0x20000578, 0x4 Bytes, Flags = 0x02000000)
T2BFC 031:532.157   CPU_ReadMem(64 bytes @ 0x20000540)
T2BFC 031:533.785    -- Updating C cache (64 bytes @ 0x20000540)
T2BFC 031:533.812    -- Read from C cache (4 bytes @ 0x20000578)
T2BFC 031:533.831   Data:  0C 00 00 20
T2BFC 031:533.849 - 1.728ms returns 4 (0x4)
T2BFC 031:533.868 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T2BFC 031:533.885    -- Read from C cache (4 bytes @ 0x2000057C)
T2BFC 031:533.904   Data:  5C 28 00 08
T2BFC 031:533.922 - 0.059ms returns 4 (0x4)
T2BFC 031:533.937 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T2BFC 031:533.951    -- Read from C cache (4 bytes @ 0x20000580)
T2BFC 031:533.969   Data:  00 00 00 00
T2BFC 031:533.988 - 0.056ms returns 4 (0x4)
T2BFC 031:534.003 JLINK_ReadMemEx(0x20000584, 0x4 Bytes, Flags = 0x02000000)
T2BFC 031:534.017    -- Read from C cache (4 bytes @ 0x20000584)
T2BFC 031:534.035   Data:  FB 27 00 08
T2BFC 031:534.053 - 0.056ms returns 4 (0x4)
T2BFC 031:534.086 JLINK_ReadMemEx(0x20000558, 0x10 Bytes, Flags = 0x02000000)
T2BFC 031:534.101    -- Read from C cache (16 bytes @ 0x20000558)
T2BFC 031:534.120   Data:  03 01 03 02 00 00 00 00 00 00 00 00 00 00 00 00
T2BFC 031:534.138 - 0.057ms returns 16 (0x10)
T2BFC 031:534.157 JLINK_ReadMemEx(0x20000568, 0x10 Bytes, Flags = 0x02000000)
T2BFC 031:534.172    -- Read from C cache (16 bytes @ 0x20000568)
T2BFC 031:534.191   Data:  00 00 00 00 00 00 00 00 00 00 00 00 01 00 00 00
T2BFC 031:534.213 - 0.062ms returns 16 (0x10)
T2BFC 031:534.256 JLINK_HasError()
T2BFC 031:534.273 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T2BFC 031:534.293   CPU_ReadMem(4 bytes @ 0xE0001004)
T2BFC 031:535.195   Data:  70 E2 CB 09
T2BFC 031:535.223   Debug reg: DWT_CYCCNT
T2BFC 031:535.242 - 0.974ms returns 1 (0x1)
T2BFC 031:536.774 JLINK_ReadMemEx(0x2000055B, 0x1 Bytes, Flags = 0x02000000)
T2BFC 031:536.803    -- Read from C cache (1 bytes @ 0x2000055B)
T2BFC 031:536.822   Data:  02
T2BFC 031:536.841 - 0.073ms returns 1 (0x1)
T2BFC 031:537.091 JLINK_ReadMemEx(0x2000055B, 0x1 Bytes, Flags = 0x02000000)
T2BFC 031:537.112    -- Read from C cache (1 bytes @ 0x2000055B)
T2BFC 031:537.159   Data:  02
T2BFC 031:537.186 - 0.108ms returns 1 (0x1)
T2BFC 031:538.603 JLINK_ReadMemEx(0x2000002E, 0x2 Bytes, Flags = 0x02000000)
T2BFC 031:538.631   CPU_ReadMem(64 bytes @ 0x20000000)
T2BFC 031:540.156    -- Updating C cache (64 bytes @ 0x20000000)
T2BFC 031:540.194    -- Read from C cache (2 bytes @ 0x2000002E)
T2BFC 031:540.216   Data:  02 03
T2BFC 031:540.234 - 1.638ms returns 2 (0x2)
T2BFC 031:540.258 JLINK_WriteMemEx(0x2000055B, 0x00000001 Bytes, Flags = 0x02000000)
T2BFC 031:540.272   Data:  02
T2BFC 031:540.301   CPU_WriteMem(1 bytes @ 0x2000055B)
T2BFC 031:541.347 - 1.110ms returns 0x1
T2BFC 031:541.771 JLINK_ReadMemEx(0x2000055A, 0x1 Bytes, Flags = 0x02000000)
T2BFC 031:541.799   CPU_ReadMem(64 bytes @ 0x20000540)
T2BFC 031:543.230    -- Updating C cache (64 bytes @ 0x20000540)
T2BFC 031:543.260    -- Read from C cache (1 bytes @ 0x2000055A)
T2BFC 031:543.279   Data:  03
T2BFC 031:543.299 - 1.534ms returns 1 (0x1)
T2BFC 031:544.565 JLINK_ReadMemEx(0x2000002E, 0x2 Bytes, Flags = 0x02000000)
T2BFC 031:544.589    -- Read from C cache (2 bytes @ 0x2000002E)
T2BFC 031:544.608   Data:  02 03
T2BFC 031:544.626 - 0.068ms returns 2 (0x2)
T2BFC 031:544.652 JLINK_WriteMemEx(0x2000055A, 0x00000001 Bytes, Flags = 0x02000000)
T2BFC 031:544.665   Data:  03
T2BFC 031:544.690   CPU_WriteMem(1 bytes @ 0x2000055A)
T2BFC 031:545.730 - 1.093ms returns 0x1
T15FC 031:998.062 JLINK_ReadMemEx(0x08002222, 0x2 Bytes, Flags = 0x02000000)
T15FC 031:998.113    -- Read from flash cache (2 bytes @ 0x08002222)
T15FC 031:998.141   Data:  05 EB
T15FC 031:998.168 - 0.115ms returns 2 (0x2)
T15FC 031:998.191 JLINK_ReadMemEx(0x08002224, 0x3C Bytes, Flags = 0x02000000)
T15FC 031:998.211    -- Read from flash cache (60 bytes @ 0x08002224)
T15FC 031:998.237    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 031:998.263    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 031:998.289    -- Merging zombie BP[4]: 0x5D28 @ 0x08002258
T15FC 031:998.316   Data:  40 03 40 1C 59 70 08 28 F4 DB 10 22 29 46 00 20 ...
T15FC 031:998.341 - 0.158ms returns 60 (0x3C)
T15FC 031:998.362 JLINK_ReadMemEx(0x08002224, 0x2 Bytes, Flags = 0x02000000)
T15FC 031:998.381    -- Read from flash cache (2 bytes @ 0x08002224)
T15FC 031:998.407   Data:  40 03
T15FC 031:998.432 - 0.079ms returns 2 (0x2)
T15FC 031:998.454 JLINK_ReadMemEx(0x08002222, 0x2 Bytes, Flags = 0x02000000)
T15FC 031:998.474    -- Read from flash cache (2 bytes @ 0x08002222)
T15FC 031:998.501   Data:  05 EB
T15FC 031:998.527 - 0.081ms returns 2 (0x2)
T15FC 031:998.549 JLINK_HasError()
T15FC 031:998.575 JLINK_Step()
T15FC 031:998.602    -- Read from flash cache (2 bytes @ 0x08002222)
T15FC 031:998.630    -- Read from flash cache (2 bytes @ 0x08002224)
T15FC 031:998.656   -- Not simulated
T15FC 032:002.938 - 4.389ms returns 0
T15FC 032:002.980 JLINK_HasError()
T15FC 032:002.998 JLINK_ReadReg(R15 (PC))
T15FC 032:003.014 - 0.022ms returns 0x08002226
T15FC 032:003.030 JLINK_ReadReg(XPSR)
T15FC 032:003.044 - 0.020ms returns 0x01000000
T15FC 032:003.070 JLINK_ReadMemEx(0x08002224, 0x3C Bytes, Flags = 0x02000000)
T15FC 032:003.086    -- Read from flash cache (60 bytes @ 0x08002224)
T15FC 032:003.105    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 032:003.124    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 032:003.148    -- Merging zombie BP[4]: 0x5D28 @ 0x08002258
T15FC 032:003.168   Data:  40 03 40 1C 59 70 08 28 F4 DB 10 22 29 46 00 20 ...
T15FC 032:003.186 - 0.122ms returns 60 (0x3C)
T15FC 032:003.202 JLINK_ReadMemEx(0x08002224, 0x2 Bytes, Flags = 0x02000000)
T15FC 032:003.216    -- Read from flash cache (2 bytes @ 0x08002224)
T15FC 032:003.234   Data:  40 03
T15FC 032:003.253 - 0.057ms returns 2 (0x2)
T15FC 032:003.269 JLINK_ReadMemEx(0x08002226, 0x2 Bytes, Flags = 0x02000000)
T15FC 032:003.283    -- Read from flash cache (2 bytes @ 0x08002226)
T15FC 032:003.301   Data:  40 1C
T15FC 032:003.319 - 0.056ms returns 2 (0x2)
T15FC 032:003.335 JLINK_ReadMemEx(0x08002226, 0x2 Bytes, Flags = 0x02000000)
T15FC 032:003.348    -- Read from flash cache (2 bytes @ 0x08002226)
T15FC 032:003.367   Data:  40 1C
T15FC 032:003.385 - 0.056ms returns 2 (0x2)
T15FC 032:003.401 JLINK_ReadMemEx(0x08002228, 0x3C Bytes, Flags = 0x02000000)
T15FC 032:003.414    -- Read from flash cache (60 bytes @ 0x08002228)
T15FC 032:003.433    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 032:003.451    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 032:003.469    -- Merging zombie BP[4]: 0x5D28 @ 0x08002258
T15FC 032:003.488   Data:  59 70 08 28 F4 DB 10 22 29 46 00 20 FE F7 59 F8 ...
T15FC 032:003.506 - 0.111ms returns 60 (0x3C)
T15FC 032:003.521 JLINK_ReadMemEx(0x08002228, 0x2 Bytes, Flags = 0x02000000)
T15FC 032:003.534    -- Read from flash cache (2 bytes @ 0x08002228)
T15FC 032:003.552   Data:  59 70
T15FC 032:003.571 - 0.055ms returns 2 (0x2)
T15FC 032:003.587 JLINK_HasError()
T15FC 032:003.602 JLINK_Step()
T15FC 032:003.616    -- Read from flash cache (2 bytes @ 0x08002226)
T15FC 032:003.637   CPU_ReadMem(4 bytes @ 0xE0001004)
T15FC 032:004.296   -- Simulated
T15FC 032:004.326 - 0.730ms returns 0
T15FC 032:004.345 JLINK_HasError()
T15FC 032:004.362 JLINK_ReadReg(R15 (PC))
T15FC 032:005.024 - 0.676ms returns 0x08002228
T15FC 032:005.053 JLINK_ReadReg(XPSR)
T15FC 032:005.069 - 0.023ms returns 0x01000000
T15FC 032:005.091 JLINK_ReadMemEx(0x08002228, 0x3C Bytes, Flags = 0x02000000)
T15FC 032:005.107    -- Read from flash cache (60 bytes @ 0x08002228)
T15FC 032:005.127    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 032:005.147    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 032:005.166    -- Merging zombie BP[4]: 0x5D28 @ 0x08002258
T15FC 032:005.186   Data:  59 70 08 28 F4 DB 10 22 29 46 00 20 FE F7 59 F8 ...
T15FC 032:005.204 - 0.119ms returns 60 (0x3C)
T15FC 032:005.220 JLINK_ReadMemEx(0x08002228, 0x2 Bytes, Flags = 0x02000000)
T15FC 032:005.234    -- Read from flash cache (2 bytes @ 0x08002228)
T15FC 032:005.253   Data:  59 70
T15FC 032:005.272 - 0.058ms returns 2 (0x2)
T15FC 032:005.288 JLINK_ReadMemEx(0x0800222A, 0x2 Bytes, Flags = 0x02000000)
T15FC 032:005.302    -- Read from flash cache (2 bytes @ 0x0800222A)
T15FC 032:005.321   Data:  08 28
T15FC 032:005.340 - 0.058ms returns 2 (0x2)
T15FC 032:005.357 JLINK_HasError()
T15FC 032:005.373 JLINK_Step()
T15FC 032:005.388    -- Read from flash cache (2 bytes @ 0x08002228)
T15FC 032:005.412   CPU_WriteMem(1 bytes @ 0x2000055B)
T15FC 032:006.109   -- Simulated
T15FC 032:006.137 - 0.770ms returns 0
T15FC 032:006.156 JLINK_HasError()
T15FC 032:006.173 JLINK_ReadReg(R15 (PC))
T15FC 032:006.189 - 0.022ms returns 0x0800222A
T15FC 032:006.206 JLINK_ReadReg(XPSR)
T15FC 032:006.220 - 0.020ms returns 0x01000000
T15FC 032:006.324 JLINK_HasError()
T15FC 032:006.347 JLINK_ReadReg(R0)
T15FC 032:006.362 - 0.022ms returns 0x00000002
T15FC 032:006.379 JLINK_ReadReg(R1)
T15FC 032:006.392 - 0.020ms returns 0x00000302
T15FC 032:006.409 JLINK_ReadReg(R2)
T15FC 032:006.422 - 0.019ms returns 0x2000002C
T15FC 032:006.438 JLINK_ReadReg(R3)
T15FC 032:006.451 - 0.019ms returns 0x2000055A
T15FC 032:006.467 JLINK_ReadReg(R4)
T15FC 032:006.481 - 0.020ms returns 0x2000000C
T15FC 032:006.497 JLINK_ReadReg(R5)
T15FC 032:006.510 - 0.019ms returns 0x20000558
T15FC 032:006.525 JLINK_ReadReg(R6)
T15FC 032:006.539 - 0.019ms returns 0x00000000
T15FC 032:006.557 JLINK_ReadReg(R7)
T15FC 032:006.572 - 0.021ms returns 0x00000000
T15FC 032:006.588 JLINK_ReadReg(R8)
T15FC 032:006.601 - 0.020ms returns 0x00000000
T15FC 032:006.617 JLINK_ReadReg(R9)
T15FC 032:006.631 - 0.020ms returns 0x00000000
T15FC 032:006.646 JLINK_ReadReg(R10)
T15FC 032:006.660 - 0.019ms returns 0x00000000
T15FC 032:006.676 JLINK_ReadReg(R11)
T15FC 032:006.689 - 0.019ms returns 0x00000000
T15FC 032:006.705 JLINK_ReadReg(R12)
T15FC 032:006.718 - 0.020ms returns 0x0000000E
T15FC 032:006.734 JLINK_ReadReg(R13 (SP))
T15FC 032:006.748 - 0.020ms returns 0x20000558
T15FC 032:006.764 JLINK_ReadReg(R14)
T15FC 032:006.777 - 0.019ms returns 0x080027FB
T15FC 032:006.793 JLINK_ReadReg(R15 (PC))
T15FC 032:006.806 - 0.019ms returns 0x0800222A
T15FC 032:006.822 JLINK_ReadReg(XPSR)
T15FC 032:006.835 - 0.019ms returns 0x01000000
T15FC 032:006.851 JLINK_ReadReg(MSP)
T15FC 032:006.866 - 0.028ms returns 0x20000558
T15FC 032:006.897 JLINK_ReadReg(PSP)
T15FC 032:006.915 - 0.025ms returns 0x00000000
T15FC 032:006.931 JLINK_ReadReg(CFBP)
T15FC 032:006.944 - 0.019ms returns 0x00000000
T15FC 032:006.960 JLINK_ReadReg(FPSCR)
T15FC 032:012.226 - 5.300ms returns 0x00000000
T15FC 032:012.282 JLINK_ReadReg(FPS0)
T15FC 032:012.318 - 0.051ms returns 0x00000000
T15FC 032:012.347 JLINK_ReadReg(FPS1)
T15FC 032:012.371 - 0.030ms returns 0x00000000
T15FC 032:012.388 JLINK_ReadReg(FPS2)
T15FC 032:012.401 - 0.019ms returns 0x00000000
T15FC 032:012.417 JLINK_ReadReg(FPS3)
T15FC 032:012.430 - 0.019ms returns 0x00000000
T15FC 032:012.445 JLINK_ReadReg(FPS4)
T15FC 032:012.458 - 0.019ms returns 0x00000000
T15FC 032:012.474 JLINK_ReadReg(FPS5)
T15FC 032:012.486 - 0.019ms returns 0x00000000
T15FC 032:012.502 JLINK_ReadReg(FPS6)
T15FC 032:012.515 - 0.019ms returns 0x00000000
T15FC 032:012.530 JLINK_ReadReg(FPS7)
T15FC 032:012.543 - 0.019ms returns 0x00000000
T15FC 032:012.558 JLINK_ReadReg(FPS8)
T15FC 032:012.571 - 0.019ms returns 0x00000000
T15FC 032:012.586 JLINK_ReadReg(FPS9)
T15FC 032:012.599 - 0.019ms returns 0x00000000
T15FC 032:012.614 JLINK_ReadReg(FPS10)
T15FC 032:012.627 - 0.019ms returns 0x00000000
T15FC 032:012.642 JLINK_ReadReg(FPS11)
T15FC 032:012.655 - 0.019ms returns 0x00000000
T15FC 032:012.672 JLINK_ReadReg(FPS12)
T15FC 032:012.703 - 0.042ms returns 0x00000000
T15FC 032:012.730 JLINK_ReadReg(FPS13)
T15FC 032:012.750 - 0.030ms returns 0x00000000
T15FC 032:012.778 JLINK_ReadReg(FPS14)
T15FC 032:012.797 - 0.024ms returns 0x00000000
T15FC 032:012.812 JLINK_ReadReg(FPS15)
T15FC 032:012.825 - 0.019ms returns 0x00000000
T15FC 032:012.841 JLINK_ReadReg(FPS16)
T15FC 032:012.856 - 0.024ms returns 0x00000000
T15FC 032:012.878 JLINK_ReadReg(FPS17)
T15FC 032:012.905 - 0.037ms returns 0x00000000
T15FC 032:012.925 JLINK_ReadReg(FPS18)
T15FC 032:012.939 - 0.020ms returns 0x00000000
T15FC 032:012.955 JLINK_ReadReg(FPS19)
T15FC 032:012.968 - 0.019ms returns 0x00000000
T15FC 032:012.983 JLINK_ReadReg(FPS20)
T15FC 032:012.996 - 0.019ms returns 0x00000000
T15FC 032:013.012 JLINK_ReadReg(FPS21)
T15FC 032:013.025 - 0.019ms returns 0x00000000
T15FC 032:013.040 JLINK_ReadReg(FPS22)
T15FC 032:013.053 - 0.021ms returns 0x00000000
T15FC 032:013.070 JLINK_ReadReg(FPS23)
T15FC 032:013.083 - 0.019ms returns 0x00000000
T15FC 032:013.099 JLINK_ReadReg(FPS24)
T15FC 032:013.112 - 0.018ms returns 0x00000000
T15FC 032:013.127 JLINK_ReadReg(FPS25)
T15FC 032:013.140 - 0.019ms returns 0x00000000
T15FC 032:013.155 JLINK_ReadReg(FPS26)
T15FC 032:013.168 - 0.019ms returns 0x00000000
T15FC 032:013.184 JLINK_ReadReg(FPS27)
T15FC 032:013.196 - 0.019ms returns 0x00000000
T15FC 032:013.212 JLINK_ReadReg(FPS28)
T15FC 032:013.225 - 0.019ms returns 0x00000000
T15FC 032:013.241 JLINK_ReadReg(FPS29)
T15FC 032:013.254 - 0.019ms returns 0x00000000
T15FC 032:013.269 JLINK_ReadReg(FPS30)
T15FC 032:013.282 - 0.019ms returns 0x00000000
T15FC 032:013.297 JLINK_ReadReg(FPS31)
T15FC 032:013.310 - 0.019ms returns 0x00000000
T2BFC 032:013.457 JLINK_ReadMemEx(0x20000584, 0x4 Bytes, Flags = 0x02000000)
T2BFC 032:013.494   CPU_ReadMem(64 bytes @ 0x20000580)
T2BFC 032:014.785    -- Updating C cache (64 bytes @ 0x20000580)
T2BFC 032:014.814    -- Read from C cache (4 bytes @ 0x20000584)
T2BFC 032:014.834   Data:  FB 27 00 08
T2BFC 032:014.862 - 1.415ms returns 4 (0x4)
T2BFC 032:014.896 JLINK_ReadMemEx(0x20000578, 0x4 Bytes, Flags = 0x02000000)
T2BFC 032:014.922   CPU_ReadMem(64 bytes @ 0x20000540)
T2BFC 032:016.344    -- Updating C cache (64 bytes @ 0x20000540)
T2BFC 032:016.372    -- Read from C cache (4 bytes @ 0x20000578)
T2BFC 032:016.391   Data:  0C 00 00 20
T2BFC 032:016.409 - 1.520ms returns 4 (0x4)
T2BFC 032:016.429 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T2BFC 032:016.446    -- Read from C cache (4 bytes @ 0x2000057C)
T2BFC 032:016.464   Data:  5C 28 00 08
T2BFC 032:016.482 - 0.060ms returns 4 (0x4)
T2BFC 032:016.498 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T2BFC 032:016.512    -- Read from C cache (4 bytes @ 0x20000580)
T2BFC 032:016.531   Data:  00 00 00 00
T2BFC 032:016.549 - 0.056ms returns 4 (0x4)
T2BFC 032:016.564 JLINK_ReadMemEx(0x20000584, 0x4 Bytes, Flags = 0x02000000)
T2BFC 032:016.578    -- Read from C cache (4 bytes @ 0x20000584)
T2BFC 032:016.596   Data:  FB 27 00 08
T2BFC 032:016.615 - 0.056ms returns 4 (0x4)
T2BFC 032:016.650 JLINK_ReadMemEx(0x20000558, 0x10 Bytes, Flags = 0x02000000)
T2BFC 032:016.665    -- Read from C cache (16 bytes @ 0x20000558)
T2BFC 032:016.684   Data:  03 01 03 02 00 00 00 00 00 00 00 00 00 00 00 00
T2BFC 032:016.702 - 0.058ms returns 16 (0x10)
T2BFC 032:016.721 JLINK_ReadMemEx(0x20000568, 0x10 Bytes, Flags = 0x02000000)
T2BFC 032:016.735    -- Read from C cache (16 bytes @ 0x20000568)
T2BFC 032:016.754   Data:  00 00 00 00 00 00 00 00 00 00 00 00 01 00 00 00
T2BFC 032:016.772 - 0.057ms returns 16 (0x10)
T2BFC 032:016.821 JLINK_HasError()
T2BFC 032:016.839 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T2BFC 032:016.855   Data:  74 E2 CB 09
T2BFC 032:016.877   Debug reg: DWT_CYCCNT
T2BFC 032:016.910 - 0.077ms returns 1 (0x1)
T2BFC 032:018.927 JLINK_ReadMemEx(0x2000055D, 0x1 Bytes, Flags = 0x02000000)
T2BFC 032:018.962    -- Read from C cache (1 bytes @ 0x2000055D)
T2BFC 032:018.981   Data:  00
T2BFC 032:019.000 - 0.079ms returns 1 (0x1)
T2BFC 032:019.265 JLINK_ReadMemEx(0x2000055D, 0x1 Bytes, Flags = 0x02000000)
T2BFC 032:019.286    -- Read from C cache (1 bytes @ 0x2000055D)
T2BFC 032:019.305   Data:  00
T2BFC 032:019.324 - 0.065ms returns 1 (0x1)
T2BFC 032:020.724 JLINK_ReadMemEx(0x20000030, 0x2 Bytes, Flags = 0x02000000)
T2BFC 032:020.754   CPU_ReadMem(64 bytes @ 0x20000000)
T2BFC 032:022.391    -- Updating C cache (64 bytes @ 0x20000000)
T2BFC 032:022.419    -- Read from C cache (2 bytes @ 0x20000030)
T2BFC 032:022.442   Data:  03 03
T2BFC 032:022.461 - 1.743ms returns 2 (0x2)
T2BFC 032:022.488 JLINK_WriteMemEx(0x2000055D, 0x00000001 Bytes, Flags = 0x02000000)
T2BFC 032:022.501   Data:  03
T2BFC 032:022.528   CPU_WriteMem(1 bytes @ 0x2000055D)
T2BFC 032:023.510 - 1.036ms returns 0x1
T2BFC 032:023.857 JLINK_ReadMemEx(0x2000055C, 0x1 Bytes, Flags = 0x02000000)
T2BFC 032:023.894   CPU_ReadMem(64 bytes @ 0x20000540)
T2BFC 032:025.612    -- Updating C cache (64 bytes @ 0x20000540)
T2BFC 032:025.698    -- Read from C cache (1 bytes @ 0x2000055C)
T2BFC 032:025.730   Data:  00
T2BFC 032:025.760 - 1.912ms returns 1 (0x1)
T2BFC 032:028.510 JLINK_ReadMemEx(0x20000030, 0x2 Bytes, Flags = 0x02000000)
T2BFC 032:028.605    -- Read from C cache (2 bytes @ 0x20000030)
T2BFC 032:028.686   Data:  03 03
T2BFC 032:028.707 - 0.205ms returns 2 (0x2)
T2BFC 032:028.730 JLINK_WriteMemEx(0x2000055C, 0x00000001 Bytes, Flags = 0x02000000)
T2BFC 032:028.743   Data:  03
T2BFC 032:028.772   CPU_WriteMem(1 bytes @ 0x2000055C)
T2BFC 032:029.822 - 1.110ms returns 0x1
T15FC 032:766.713 JLINK_ReadMemEx(0x0800222A, 0x2 Bytes, Flags = 0x02000000)
T15FC 032:766.755    -- Read from flash cache (2 bytes @ 0x0800222A)
T15FC 032:766.775   Data:  08 28
T15FC 032:766.795 - 0.087ms returns 2 (0x2)
T15FC 032:766.812 JLINK_ReadMemEx(0x0800222C, 0x3C Bytes, Flags = 0x02000000)
T15FC 032:766.834    -- Read from flash cache (60 bytes @ 0x0800222C)
T15FC 032:766.866    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 032:766.897    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 032:766.918    -- Merging zombie BP[4]: 0x5D28 @ 0x08002258
T15FC 032:766.937   Data:  F4 DB 10 22 29 46 00 20 FE F7 59 F8 0A 20 FE F7 ...
T15FC 032:766.955 - 0.149ms returns 60 (0x3C)
T15FC 032:766.972 JLINK_ReadMemEx(0x0800222C, 0x2 Bytes, Flags = 0x02000000)
T15FC 032:766.987    -- Read from flash cache (2 bytes @ 0x0800222C)
T15FC 032:767.005   Data:  F4 DB
T15FC 032:767.023 - 0.057ms returns 2 (0x2)
T15FC 032:767.040 JLINK_ReadMemEx(0x0800222A, 0x2 Bytes, Flags = 0x02000000)
T15FC 032:767.053    -- Read from flash cache (2 bytes @ 0x0800222A)
T15FC 032:767.071   Data:  08 28
T15FC 032:767.089 - 0.055ms returns 2 (0x2)
T15FC 032:767.106 JLINK_HasError()
T15FC 032:767.124 JLINK_Step()
T15FC 032:767.874    -- Read from flash cache (2 bytes @ 0x0800222A)
T15FC 032:767.920   -- Simulated
T15FC 032:767.946 - 0.830ms returns 0
T15FC 032:767.972 JLINK_HasError()
T15FC 032:767.994 JLINK_ReadReg(R15 (PC))
T15FC 032:768.016 - 0.031ms returns 0x0800222C
T15FC 032:768.038 JLINK_ReadReg(XPSR)
T15FC 032:768.058 - 0.029ms returns 0x81000000
T15FC 032:768.089 JLINK_ReadMemEx(0x0800222C, 0x3C Bytes, Flags = 0x02000000)
T15FC 032:768.109    -- Read from flash cache (60 bytes @ 0x0800222C)
T15FC 032:768.136    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 032:768.164    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 032:768.192    -- Merging zombie BP[4]: 0x5D28 @ 0x08002258
T15FC 032:768.220   Data:  F4 DB 10 22 29 46 00 20 FE F7 59 F8 0A 20 FE F7 ...
T15FC 032:768.247 - 0.167ms returns 60 (0x3C)
T15FC 032:768.271 JLINK_ReadMemEx(0x0800222C, 0x2 Bytes, Flags = 0x02000000)
T15FC 032:768.292    -- Read from flash cache (2 bytes @ 0x0800222C)
T15FC 032:768.319   Data:  F4 DB
T15FC 032:768.346 - 0.085ms returns 2 (0x2)
T15FC 032:768.369 JLINK_ReadMemEx(0x0800222E, 0x2 Bytes, Flags = 0x02000000)
T15FC 032:768.391    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 032:768.424    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 032:768.451   Data:  10 22
T15FC 032:768.477 - 0.117ms returns 2 (0x2)
T15FC 032:768.502 JLINK_HasError()
T15FC 032:768.525 JLINK_Step()
T15FC 032:768.548    -- Read from flash cache (2 bytes @ 0x0800222C)
T15FC 032:768.578   -- Simulated
T15FC 032:768.605 - 0.088ms returns 0
T15FC 032:768.628 JLINK_HasError()
T15FC 032:768.651 JLINK_ReadReg(R15 (PC))
T15FC 032:768.671 - 0.030ms returns 0x08002218
T15FC 032:768.695 JLINK_ReadReg(XPSR)
T15FC 032:768.714 - 0.029ms returns 0x81000000
T15FC 032:768.742 JLINK_ReadMemEx(0x08002216, 0x2 Bytes, Flags = 0x02000000)
T15FC 032:768.762    -- Read from flash cache (2 bytes @ 0x08002216)
T15FC 032:768.790   Data:  0D 05
T15FC 032:768.817 - 0.096ms returns 2 (0x2)
T15FC 032:768.853 JLINK_ReadMemEx(0x08002218, 0x3C Bytes, Flags = 0x02000000)
T15FC 032:768.874    -- Read from flash cache (60 bytes @ 0x08002218)
T15FC 032:768.901    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 032:768.929    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 032:768.958   Data:  32 F8 10 10 0B 0A 05 F8 10 30 05 EB 40 03 40 1C ...
T15FC 032:768.984 - 0.140ms returns 60 (0x3C)
T15FC 032:769.007 JLINK_ReadMemEx(0x08002218, 0x2 Bytes, Flags = 0x02000000)
T15FC 032:769.028    -- Read from flash cache (2 bytes @ 0x08002218)
T15FC 032:769.056   Data:  32 F8
T15FC 032:769.084 - 0.085ms returns 2 (0x2)
T15FC 032:769.108 JLINK_ReadMemEx(0x08002218, 0x3C Bytes, Flags = 0x02000000)
T15FC 032:769.129    -- Read from flash cache (60 bytes @ 0x08002218)
T15FC 032:769.157    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 032:769.184    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 032:769.212   Data:  32 F8 10 10 0B 0A 05 F8 10 30 05 EB 40 03 40 1C ...
T15FC 032:769.239 - 0.140ms returns 60 (0x3C)
T15FC 032:769.262 JLINK_ReadMemEx(0x08002218, 0x2 Bytes, Flags = 0x02000000)
T15FC 032:769.288    -- Read from flash cache (2 bytes @ 0x08002218)
T15FC 032:769.319   Data:  32 F8
T15FC 032:769.345 - 0.092ms returns 2 (0x2)
T15FC 032:769.370 JLINK_ReadMemEx(0x0800221A, 0x2 Bytes, Flags = 0x02000000)
T15FC 032:769.434    -- Read from flash cache (2 bytes @ 0x0800221A)
T15FC 032:769.464   Data:  10 10
T15FC 032:769.491 - 0.130ms returns 2 (0x2)
T15FC 032:769.516 JLINK_HasError()
T15FC 032:769.539 JLINK_Step()
T15FC 032:769.562    -- Read from flash cache (2 bytes @ 0x08002218)
T15FC 032:769.591    -- Read from flash cache (2 bytes @ 0x0800221A)
T15FC 032:769.621   -- Not simulated
T15FC 032:769.667   CPU_WriteMem(4 bytes @ 0xE0001004)
T15FC 032:776.915 - 7.403ms returns 0
T15FC 032:776.961 JLINK_HasError()
T15FC 032:776.979 JLINK_ReadReg(R15 (PC))
T15FC 032:776.995 - 0.022ms returns 0x0800221C
T15FC 032:777.013 JLINK_ReadReg(XPSR)
T15FC 032:777.027 - 0.019ms returns 0x81000000
T15FC 032:777.052 JLINK_ReadMemEx(0x0800221A, 0x2 Bytes, Flags = 0x02000000)
T15FC 032:777.068    -- Read from flash cache (2 bytes @ 0x0800221A)
T15FC 032:777.087   Data:  10 10
T15FC 032:777.105 - 0.059ms returns 2 (0x2)
T15FC 032:777.123 JLINK_ReadMemEx(0x0800221C, 0x3C Bytes, Flags = 0x02000000)
T15FC 032:777.137    -- Read from flash cache (60 bytes @ 0x0800221C)
T15FC 032:777.155    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 032:777.174    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 032:777.193   Data:  0B 0A 05 F8 10 30 05 EB 40 03 40 1C 59 70 08 28 ...
T15FC 032:777.211 - 0.093ms returns 60 (0x3C)
T15FC 032:777.228 JLINK_ReadMemEx(0x0800221C, 0x2 Bytes, Flags = 0x02000000)
T15FC 032:777.241    -- Read from flash cache (2 bytes @ 0x0800221C)
T15FC 032:777.259   Data:  0B 0A
T15FC 032:777.277 - 0.056ms returns 2 (0x2)
T15FC 032:777.295 JLINK_ReadMemEx(0x0800221C, 0x3C Bytes, Flags = 0x02000000)
T15FC 032:777.308    -- Read from flash cache (60 bytes @ 0x0800221C)
T15FC 032:777.326    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 032:777.345    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 032:777.363   Data:  0B 0A 05 F8 10 30 05 EB 40 03 40 1C 59 70 08 28 ...
T15FC 032:777.381 - 0.092ms returns 60 (0x3C)
T15FC 032:777.398 JLINK_ReadMemEx(0x0800221C, 0x2 Bytes, Flags = 0x02000000)
T15FC 032:777.411    -- Read from flash cache (2 bytes @ 0x0800221C)
T15FC 032:777.430   Data:  0B 0A
T15FC 032:777.448 - 0.056ms returns 2 (0x2)
T15FC 032:777.465 JLINK_ReadMemEx(0x0800221E, 0x2 Bytes, Flags = 0x02000000)
T15FC 032:777.478    -- Read from flash cache (2 bytes @ 0x0800221E)
T15FC 032:777.496   Data:  05 F8
T15FC 032:777.514 - 0.055ms returns 2 (0x2)
T15FC 032:777.531 JLINK_HasError()
T15FC 032:777.547 JLINK_Step()
T15FC 032:777.561    -- Read from flash cache (2 bytes @ 0x0800221C)
T15FC 032:777.582   CPU_ReadMem(4 bytes @ 0xE0001004)
T15FC 032:778.433   -- Simulated
T15FC 032:778.461 - 0.920ms returns 0
T15FC 032:778.482 JLINK_HasError()
T15FC 032:778.498 JLINK_ReadReg(R15 (PC))
T15FC 032:778.514 - 0.022ms returns 0x0800221E
T15FC 032:778.532 JLINK_ReadReg(XPSR)
T15FC 032:778.545 - 0.019ms returns 0x01000000
T15FC 032:778.567 JLINK_ReadMemEx(0x0800221E, 0x2 Bytes, Flags = 0x02000000)
T15FC 032:778.581    -- Read from flash cache (2 bytes @ 0x0800221E)
T15FC 032:778.600   Data:  05 F8
T15FC 032:778.619 - 0.058ms returns 2 (0x2)
T15FC 032:778.636 JLINK_ReadMemEx(0x08002220, 0x3C Bytes, Flags = 0x02000000)
T15FC 032:778.650    -- Read from flash cache (60 bytes @ 0x08002220)
T15FC 032:778.668    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 032:778.687    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 032:778.705    -- Merging zombie BP[4]: 0x5D28 @ 0x08002258
T15FC 032:778.724   Data:  10 30 05 EB 40 03 40 1C 59 70 08 28 F4 DB 10 22 ...
T15FC 032:778.742 - 0.111ms returns 60 (0x3C)
T15FC 032:778.759 JLINK_ReadMemEx(0x08002220, 0x2 Bytes, Flags = 0x02000000)
T15FC 032:778.772    -- Read from flash cache (2 bytes @ 0x08002220)
T15FC 032:778.790   Data:  10 30
T15FC 032:778.810 - 0.057ms returns 2 (0x2)
T15FC 032:778.830 JLINK_HasError()
T15FC 032:778.847 JLINK_Step()
T15FC 032:778.862    -- Read from flash cache (2 bytes @ 0x0800221E)
T15FC 032:778.881    -- Read from flash cache (2 bytes @ 0x08002220)
T15FC 032:778.900   -- Not simulated
T15FC 032:778.927   CPU_WriteMem(4 bytes @ 0xE0001004)
T15FC 032:786.120 - 7.298ms returns 0
T15FC 032:786.163 JLINK_HasError()
T15FC 032:786.180 JLINK_ReadReg(R15 (PC))
T15FC 032:786.196 - 0.022ms returns 0x08002222
T15FC 032:786.214 JLINK_ReadReg(XPSR)
T15FC 032:786.227 - 0.019ms returns 0x01000000
T15FC 032:786.336 JLINK_HasError()
T15FC 032:786.360 JLINK_ReadReg(R0)
T15FC 032:786.376 - 0.022ms returns 0x00000002
T15FC 032:786.392 JLINK_ReadReg(R1)
T15FC 032:786.406 - 0.020ms returns 0x00000303
T15FC 032:786.422 JLINK_ReadReg(R2)
T15FC 032:786.435 - 0.020ms returns 0x2000002C
T15FC 032:786.451 JLINK_ReadReg(R3)
T15FC 032:786.464 - 0.020ms returns 0x00000003
T15FC 032:786.480 JLINK_ReadReg(R4)
T15FC 032:786.493 - 0.019ms returns 0x2000000C
T15FC 032:786.509 JLINK_ReadReg(R5)
T15FC 032:786.523 - 0.019ms returns 0x20000558
T15FC 032:786.538 JLINK_ReadReg(R6)
T15FC 032:786.553 - 0.021ms returns 0x00000000
T15FC 032:786.569 JLINK_ReadReg(R7)
T15FC 032:786.582 - 0.019ms returns 0x00000000
T15FC 032:786.598 JLINK_ReadReg(R8)
T15FC 032:786.611 - 0.020ms returns 0x00000000
T15FC 032:786.627 JLINK_ReadReg(R9)
T15FC 032:786.640 - 0.019ms returns 0x00000000
T15FC 032:786.656 JLINK_ReadReg(R10)
T15FC 032:786.669 - 0.019ms returns 0x00000000
T15FC 032:786.684 JLINK_ReadReg(R11)
T15FC 032:786.698 - 0.019ms returns 0x00000000
T15FC 032:786.713 JLINK_ReadReg(R12)
T15FC 032:786.726 - 0.019ms returns 0x0000000E
T15FC 032:786.742 JLINK_ReadReg(R13 (SP))
T15FC 032:786.755 - 0.020ms returns 0x20000558
T15FC 032:786.771 JLINK_ReadReg(R14)
T15FC 032:786.784 - 0.019ms returns 0x080027FB
T15FC 032:786.800 JLINK_ReadReg(R15 (PC))
T15FC 032:786.815 - 0.021ms returns 0x08002222
T15FC 032:786.830 JLINK_ReadReg(XPSR)
T15FC 032:786.843 - 0.019ms returns 0x01000000
T15FC 032:786.859 JLINK_ReadReg(MSP)
T15FC 032:786.872 - 0.019ms returns 0x20000558
T15FC 032:786.888 JLINK_ReadReg(PSP)
T15FC 032:786.901 - 0.019ms returns 0x00000000
T15FC 032:786.917 JLINK_ReadReg(CFBP)
T15FC 032:786.930 - 0.019ms returns 0x00000000
T15FC 032:786.945 JLINK_ReadReg(FPSCR)
T15FC 032:792.620 - 5.695ms returns 0x00000000
T15FC 032:792.654 JLINK_ReadReg(FPS0)
T15FC 032:792.670 - 0.022ms returns 0x00000000
T15FC 032:792.686 JLINK_ReadReg(FPS1)
T15FC 032:792.699 - 0.019ms returns 0x00000000
T15FC 032:792.714 JLINK_ReadReg(FPS2)
T15FC 032:792.727 - 0.019ms returns 0x00000000
T15FC 032:792.742 JLINK_ReadReg(FPS3)
T15FC 032:792.755 - 0.019ms returns 0x00000000
T15FC 032:792.770 JLINK_ReadReg(FPS4)
T15FC 032:792.783 - 0.019ms returns 0x00000000
T15FC 032:792.800 JLINK_ReadReg(FPS5)
T15FC 032:792.826 - 0.040ms returns 0x00000000
T15FC 032:792.852 JLINK_ReadReg(FPS6)
T15FC 032:792.867 - 0.021ms returns 0x00000000
T15FC 032:792.883 JLINK_ReadReg(FPS7)
T15FC 032:792.897 - 0.020ms returns 0x00000000
T15FC 032:792.912 JLINK_ReadReg(FPS8)
T15FC 032:792.925 - 0.019ms returns 0x00000000
T15FC 032:792.940 JLINK_ReadReg(FPS9)
T15FC 032:792.953 - 0.019ms returns 0x00000000
T15FC 032:792.968 JLINK_ReadReg(FPS10)
T15FC 032:792.981 - 0.019ms returns 0x00000000
T15FC 032:792.996 JLINK_ReadReg(FPS11)
T15FC 032:793.009 - 0.019ms returns 0x00000000
T15FC 032:793.024 JLINK_ReadReg(FPS12)
T15FC 032:793.037 - 0.019ms returns 0x00000000
T15FC 032:793.052 JLINK_ReadReg(FPS13)
T15FC 032:793.065 - 0.019ms returns 0x00000000
T15FC 032:793.080 JLINK_ReadReg(FPS14)
T15FC 032:793.093 - 0.019ms returns 0x00000000
T15FC 032:793.108 JLINK_ReadReg(FPS15)
T15FC 032:793.121 - 0.019ms returns 0x00000000
T15FC 032:793.136 JLINK_ReadReg(FPS16)
T15FC 032:793.149 - 0.019ms returns 0x00000000
T15FC 032:793.164 JLINK_ReadReg(FPS17)
T15FC 032:793.177 - 0.019ms returns 0x00000000
T15FC 032:793.192 JLINK_ReadReg(FPS18)
T15FC 032:793.205 - 0.019ms returns 0x00000000
T15FC 032:793.220 JLINK_ReadReg(FPS19)
T15FC 032:793.236 - 0.024ms returns 0x00000000
T15FC 032:793.254 JLINK_ReadReg(FPS20)
T15FC 032:793.266 - 0.019ms returns 0x00000000
T15FC 032:793.282 JLINK_ReadReg(FPS21)
T15FC 032:793.295 - 0.019ms returns 0x00000000
T15FC 032:793.311 JLINK_ReadReg(FPS22)
T15FC 032:793.323 - 0.019ms returns 0x00000000
T15FC 032:793.339 JLINK_ReadReg(FPS23)
T15FC 032:793.351 - 0.019ms returns 0x00000000
T15FC 032:793.367 JLINK_ReadReg(FPS24)
T15FC 032:793.380 - 0.019ms returns 0x00000000
T15FC 032:793.395 JLINK_ReadReg(FPS25)
T15FC 032:793.408 - 0.019ms returns 0x00000000
T15FC 032:793.423 JLINK_ReadReg(FPS26)
T15FC 032:793.436 - 0.019ms returns 0x00000000
T15FC 032:793.451 JLINK_ReadReg(FPS27)
T15FC 032:793.464 - 0.019ms returns 0x00000000
T15FC 032:793.479 JLINK_ReadReg(FPS28)
T15FC 032:793.492 - 0.019ms returns 0x00000000
T15FC 032:793.507 JLINK_ReadReg(FPS29)
T15FC 032:793.520 - 0.019ms returns 0x00000000
T15FC 032:793.535 JLINK_ReadReg(FPS30)
T15FC 032:793.548 - 0.019ms returns 0x00000000
T15FC 032:793.563 JLINK_ReadReg(FPS31)
T15FC 032:793.576 - 0.019ms returns 0x00000000
T2BFC 032:793.809 JLINK_ReadMemEx(0x20000584, 0x4 Bytes, Flags = 0x02000000)
T2BFC 032:793.843   CPU_ReadMem(64 bytes @ 0x20000580)
T2BFC 032:795.482    -- Updating C cache (64 bytes @ 0x20000580)
T2BFC 032:795.510    -- Read from C cache (4 bytes @ 0x20000584)
T2BFC 032:795.529   Data:  FB 27 00 08
T2BFC 032:795.547 - 1.745ms returns 4 (0x4)
T2BFC 032:795.570 JLINK_ReadMemEx(0x20000578, 0x4 Bytes, Flags = 0x02000000)
T2BFC 032:795.588   CPU_ReadMem(64 bytes @ 0x20000540)
T2BFC 032:797.026    -- Updating C cache (64 bytes @ 0x20000540)
T2BFC 032:797.058    -- Read from C cache (4 bytes @ 0x20000578)
T2BFC 032:797.078   Data:  0C 00 00 20
T2BFC 032:797.096 - 1.532ms returns 4 (0x4)
T2BFC 032:797.115 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T2BFC 032:797.132    -- Read from C cache (4 bytes @ 0x2000057C)
T2BFC 032:797.151   Data:  5C 28 00 08
T2BFC 032:797.169 - 0.059ms returns 4 (0x4)
T2BFC 032:797.185 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T2BFC 032:797.199    -- Read from C cache (4 bytes @ 0x20000580)
T2BFC 032:797.217   Data:  00 00 00 00
T2BFC 032:797.235 - 0.056ms returns 4 (0x4)
T2BFC 032:797.251 JLINK_ReadMemEx(0x20000584, 0x4 Bytes, Flags = 0x02000000)
T2BFC 032:797.265    -- Read from C cache (4 bytes @ 0x20000584)
T2BFC 032:797.283   Data:  FB 27 00 08
T2BFC 032:797.301 - 0.056ms returns 4 (0x4)
T2BFC 032:797.334 JLINK_ReadMemEx(0x20000558, 0x10 Bytes, Flags = 0x02000000)
T2BFC 032:797.350    -- Read from C cache (16 bytes @ 0x20000558)
T2BFC 032:797.369   Data:  03 01 03 02 03 03 00 00 00 00 00 00 00 00 00 00
T2BFC 032:797.387 - 0.058ms returns 16 (0x10)
T2BFC 032:797.419 JLINK_ReadMemEx(0x20000568, 0x10 Bytes, Flags = 0x02000000)
T2BFC 032:797.434    -- Read from C cache (16 bytes @ 0x20000568)
T2BFC 032:797.453   Data:  00 00 00 00 00 00 00 00 00 00 00 00 01 00 00 00
T2BFC 032:797.471 - 0.058ms returns 16 (0x10)
T2BFC 032:797.509 JLINK_HasError()
T2BFC 032:797.526 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T2BFC 032:797.543   CPU_ReadMem(4 bytes @ 0xE0001004)
T2BFC 032:798.557   Data:  7B E2 CB 09
T2BFC 032:798.584   Debug reg: DWT_CYCCNT
T2BFC 032:798.602 - 1.082ms returns 1 (0x1)
T2BFC 032:800.155 JLINK_ReadMemEx(0x2000055D, 0x1 Bytes, Flags = 0x02000000)
T2BFC 032:800.184    -- Read from C cache (1 bytes @ 0x2000055D)
T2BFC 032:800.203   Data:  03
T2BFC 032:800.221 - 0.073ms returns 1 (0x1)
T2BFC 032:800.675 JLINK_ReadMemEx(0x2000055D, 0x1 Bytes, Flags = 0x02000000)
T2BFC 032:800.700    -- Read from C cache (1 bytes @ 0x2000055D)
T2BFC 032:800.719   Data:  03
T2BFC 032:800.738 - 0.068ms returns 1 (0x1)
T2BFC 032:802.334 JLINK_ReadMemEx(0x20000030, 0x2 Bytes, Flags = 0x02000000)
T2BFC 032:802.363   CPU_ReadMem(64 bytes @ 0x20000000)
T2BFC 032:803.847    -- Updating C cache (64 bytes @ 0x20000000)
T2BFC 032:803.885    -- Read from C cache (2 bytes @ 0x20000030)
T2BFC 032:803.905   Data:  03 03
T2BFC 032:803.924 - 1.596ms returns 2 (0x2)
T2BFC 032:803.951 JLINK_WriteMemEx(0x2000055D, 0x00000001 Bytes, Flags = 0x02000000)
T2BFC 032:803.966   Data:  03
T2BFC 032:803.992   CPU_WriteMem(1 bytes @ 0x2000055D)
T2BFC 032:805.092 - 1.154ms returns 0x1
T2BFC 032:805.424 JLINK_ReadMemEx(0x2000055C, 0x1 Bytes, Flags = 0x02000000)
T2BFC 032:805.449   CPU_ReadMem(64 bytes @ 0x20000540)
T2BFC 032:806.847    -- Updating C cache (64 bytes @ 0x20000540)
T2BFC 032:806.877    -- Read from C cache (1 bytes @ 0x2000055C)
T2BFC 032:806.896   Data:  03
T2BFC 032:806.915 - 1.496ms returns 1 (0x1)
T2BFC 032:808.671 JLINK_ReadMemEx(0x20000030, 0x2 Bytes, Flags = 0x02000000)
T2BFC 032:808.706    -- Read from C cache (2 bytes @ 0x20000030)
T2BFC 032:808.747   Data:  03 03
T2BFC 032:808.774 - 0.109ms returns 2 (0x2)
T2BFC 032:808.795 JLINK_WriteMemEx(0x2000055C, 0x00000001 Bytes, Flags = 0x02000000)
T2BFC 032:808.809   Data:  03
T2BFC 032:808.838   CPU_WriteMem(1 bytes @ 0x2000055C)
T2BFC 032:809.841 - 1.067ms returns 0x1
T15FC 033:559.102 JLINK_ReadMemEx(0x08002222, 0x2 Bytes, Flags = 0x02000000)
T15FC 033:559.154    -- Read from flash cache (2 bytes @ 0x08002222)
T15FC 033:559.182   Data:  05 EB
T15FC 033:559.208 - 0.116ms returns 2 (0x2)
T15FC 033:559.232 JLINK_ReadMemEx(0x08002224, 0x3C Bytes, Flags = 0x02000000)
T15FC 033:559.252    -- Read from flash cache (60 bytes @ 0x08002224)
T15FC 033:559.278    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 033:559.305    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 033:559.331    -- Merging zombie BP[4]: 0x5D28 @ 0x08002258
T15FC 033:559.358   Data:  40 03 40 1C 59 70 08 28 F4 DB 10 22 29 46 00 20 ...
T15FC 033:559.383 - 0.159ms returns 60 (0x3C)
T15FC 033:559.404 JLINK_ReadMemEx(0x08002224, 0x2 Bytes, Flags = 0x02000000)
T15FC 033:559.425    -- Read from flash cache (2 bytes @ 0x08002224)
T15FC 033:559.451   Data:  40 03
T15FC 033:559.477 - 0.081ms returns 2 (0x2)
T15FC 033:559.500 JLINK_ReadMemEx(0x08002222, 0x2 Bytes, Flags = 0x02000000)
T15FC 033:559.519    -- Read from flash cache (2 bytes @ 0x08002222)
T15FC 033:559.545   Data:  05 EB
T15FC 033:559.570 - 0.079ms returns 2 (0x2)
T15FC 033:559.593 JLINK_HasError()
T15FC 033:559.618 JLINK_Step()
T15FC 033:560.260    -- Read from flash cache (2 bytes @ 0x08002222)
T15FC 033:560.297    -- Read from flash cache (2 bytes @ 0x08002224)
T15FC 033:560.324   -- Not simulated
T15FC 033:564.652 - 5.068ms returns 0
T15FC 033:564.712 JLINK_HasError()
T15FC 033:564.744 JLINK_ReadReg(R15 (PC))
T15FC 033:564.769 - 0.034ms returns 0x08002226
T15FC 033:564.794 JLINK_ReadReg(XPSR)
T15FC 033:564.813 - 0.027ms returns 0x01000000
T15FC 033:564.883 JLINK_ReadMemEx(0x08002224, 0x3C Bytes, Flags = 0x02000000)
T15FC 033:564.909    -- Read from flash cache (60 bytes @ 0x08002224)
T15FC 033:564.936    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 033:564.961    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 033:564.985    -- Merging zombie BP[4]: 0x5D28 @ 0x08002258
T15FC 033:565.012   Data:  40 03 40 1C 59 70 08 28 F4 DB 10 22 29 46 00 20 ...
T15FC 033:565.037 - 0.162ms returns 60 (0x3C)
T15FC 033:565.059 JLINK_ReadMemEx(0x08002224, 0x2 Bytes, Flags = 0x02000000)
T15FC 033:565.078    -- Read from flash cache (2 bytes @ 0x08002224)
T15FC 033:565.103   Data:  40 03
T15FC 033:565.128 - 0.078ms returns 2 (0x2)
T15FC 033:565.151 JLINK_ReadMemEx(0x08002226, 0x2 Bytes, Flags = 0x02000000)
T15FC 033:565.170    -- Read from flash cache (2 bytes @ 0x08002226)
T15FC 033:565.195   Data:  40 1C
T15FC 033:565.219 - 0.077ms returns 2 (0x2)
T15FC 033:565.241 JLINK_ReadMemEx(0x08002226, 0x2 Bytes, Flags = 0x02000000)
T15FC 033:565.260    -- Read from flash cache (2 bytes @ 0x08002226)
T15FC 033:565.285   Data:  40 1C
T15FC 033:565.310 - 0.077ms returns 2 (0x2)
T15FC 033:565.332 JLINK_ReadMemEx(0x08002228, 0x3C Bytes, Flags = 0x02000000)
T15FC 033:565.351    -- Read from flash cache (60 bytes @ 0x08002228)
T15FC 033:565.376    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 033:565.401    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 033:565.434    -- Merging zombie BP[4]: 0x5D28 @ 0x08002258
T15FC 033:565.460   Data:  59 70 08 28 F4 DB 10 22 29 46 00 20 FE F7 59 F8 ...
T15FC 033:565.485 - 0.161ms returns 60 (0x3C)
T15FC 033:565.506 JLINK_ReadMemEx(0x08002228, 0x2 Bytes, Flags = 0x02000000)
T15FC 033:565.524    -- Read from flash cache (2 bytes @ 0x08002228)
T15FC 033:565.549   Data:  59 70
T15FC 033:565.573 - 0.076ms returns 2 (0x2)
T15FC 033:565.596 JLINK_HasError()
T15FC 033:565.617 JLINK_Step()
T15FC 033:565.638    -- Read from flash cache (2 bytes @ 0x08002226)
T15FC 033:565.666   CPU_ReadMem(4 bytes @ 0xE0001004)
T15FC 033:566.352   -- Simulated
T15FC 033:566.385 - 0.776ms returns 0
T15FC 033:566.409 JLINK_HasError()
T15FC 033:566.430 JLINK_ReadReg(R15 (PC))
T15FC 033:566.450 - 0.029ms returns 0x08002228
T15FC 033:566.472 JLINK_ReadReg(XPSR)
T15FC 033:566.490 - 0.027ms returns 0x01000000
T15FC 033:566.518 JLINK_ReadMemEx(0x08002228, 0x3C Bytes, Flags = 0x02000000)
T15FC 033:566.538    -- Read from flash cache (60 bytes @ 0x08002228)
T15FC 033:566.569    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 033:566.593    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 033:566.618    -- Merging zombie BP[4]: 0x5D28 @ 0x08002258
T15FC 033:566.643   Data:  59 70 08 28 F4 DB 10 22 29 46 00 20 FE F7 59 F8 ...
T15FC 033:566.669 - 0.159ms returns 60 (0x3C)
T15FC 033:566.695 JLINK_ReadMemEx(0x08002228, 0x2 Bytes, Flags = 0x02000000)
T15FC 033:566.715    -- Read from flash cache (2 bytes @ 0x08002228)
T15FC 033:566.740   Data:  59 70
T15FC 033:566.766 - 0.080ms returns 2 (0x2)
T15FC 033:566.788 JLINK_ReadMemEx(0x0800222A, 0x2 Bytes, Flags = 0x02000000)
T15FC 033:566.807    -- Read from flash cache (2 bytes @ 0x0800222A)
T15FC 033:566.832   Data:  08 28
T15FC 033:566.857 - 0.075ms returns 2 (0x2)
T15FC 033:566.874 JLINK_HasError()
T15FC 033:566.890 JLINK_Step()
T15FC 033:566.905    -- Read from flash cache (2 bytes @ 0x08002228)
T15FC 033:566.930   CPU_WriteMem(1 bytes @ 0x2000055D)
T15FC 033:567.628   -- Simulated
T15FC 033:567.656 - 0.772ms returns 0
T15FC 033:567.675 JLINK_HasError()
T15FC 033:567.694 JLINK_ReadReg(R15 (PC))
T15FC 033:567.721 - 0.039ms returns 0x0800222A
T15FC 033:567.747 JLINK_ReadReg(XPSR)
T15FC 033:567.762 - 0.021ms returns 0x01000000
T15FC 033:567.896 JLINK_HasError()
T15FC 033:567.922 JLINK_ReadReg(R0)
T15FC 033:567.938 - 0.023ms returns 0x00000003
T15FC 033:567.955 JLINK_ReadReg(R1)
T15FC 033:567.969 - 0.020ms returns 0x00000303
T15FC 033:567.985 JLINK_ReadReg(R2)
T15FC 033:567.999 - 0.020ms returns 0x2000002C
T15FC 033:568.014 JLINK_ReadReg(R3)
T15FC 033:568.028 - 0.019ms returns 0x2000055C
T15FC 033:568.044 JLINK_ReadReg(R4)
T15FC 033:568.057 - 0.019ms returns 0x2000000C
T15FC 033:568.072 JLINK_ReadReg(R5)
T15FC 033:568.085 - 0.019ms returns 0x20000558
T15FC 033:568.101 JLINK_ReadReg(R6)
T15FC 033:568.114 - 0.019ms returns 0x00000000
T15FC 033:568.130 JLINK_ReadReg(R7)
T15FC 033:568.143 - 0.019ms returns 0x00000000
T15FC 033:568.159 JLINK_ReadReg(R8)
T15FC 033:568.172 - 0.019ms returns 0x00000000
T15FC 033:568.188 JLINK_ReadReg(R9)
T15FC 033:568.201 - 0.019ms returns 0x00000000
T15FC 033:568.216 JLINK_ReadReg(R10)
T15FC 033:568.229 - 0.019ms returns 0x00000000
T15FC 033:568.245 JLINK_ReadReg(R11)
T15FC 033:568.258 - 0.019ms returns 0x00000000
T15FC 033:568.274 JLINK_ReadReg(R12)
T15FC 033:568.287 - 0.021ms returns 0x0000000E
T15FC 033:568.305 JLINK_ReadReg(R13 (SP))
T15FC 033:568.318 - 0.019ms returns 0x20000558
T15FC 033:568.334 JLINK_ReadReg(R14)
T15FC 033:568.355 - 0.027ms returns 0x080027FB
T15FC 033:568.370 JLINK_ReadReg(R15 (PC))
T15FC 033:568.384 - 0.019ms returns 0x0800222A
T15FC 033:568.399 JLINK_ReadReg(XPSR)
T15FC 033:568.412 - 0.019ms returns 0x01000000
T15FC 033:568.428 JLINK_ReadReg(MSP)
T15FC 033:568.441 - 0.019ms returns 0x20000558
T15FC 033:568.457 JLINK_ReadReg(PSP)
T15FC 033:568.470 - 0.019ms returns 0x00000000
T15FC 033:568.486 JLINK_ReadReg(CFBP)
T15FC 033:568.499 - 0.019ms returns 0x00000000
T15FC 033:568.514 JLINK_ReadReg(FPSCR)
T15FC 033:573.748 - 5.262ms returns 0x00000000
T15FC 033:573.792 JLINK_ReadReg(FPS0)
T15FC 033:573.809 - 0.023ms returns 0x00000000
T15FC 033:573.825 JLINK_ReadReg(FPS1)
T15FC 033:573.838 - 0.019ms returns 0x00000000
T15FC 033:573.854 JLINK_ReadReg(FPS2)
T15FC 033:573.867 - 0.019ms returns 0x00000000
T15FC 033:573.882 JLINK_ReadReg(FPS3)
T15FC 033:573.895 - 0.019ms returns 0x00000000
T15FC 033:573.910 JLINK_ReadReg(FPS4)
T15FC 033:573.922 - 0.018ms returns 0x00000000
T15FC 033:573.938 JLINK_ReadReg(FPS5)
T15FC 033:573.950 - 0.019ms returns 0x00000000
T15FC 033:573.966 JLINK_ReadReg(FPS6)
T15FC 033:573.978 - 0.019ms returns 0x00000000
T15FC 033:573.994 JLINK_ReadReg(FPS7)
T15FC 033:574.008 - 0.020ms returns 0x00000000
T15FC 033:574.024 JLINK_ReadReg(FPS8)
T15FC 033:574.036 - 0.018ms returns 0x00000000
T15FC 033:574.051 JLINK_ReadReg(FPS9)
T15FC 033:574.064 - 0.018ms returns 0x00000000
T15FC 033:574.079 JLINK_ReadReg(FPS10)
T15FC 033:574.092 - 0.019ms returns 0x00000000
T15FC 033:574.107 JLINK_ReadReg(FPS11)
T15FC 033:574.120 - 0.018ms returns 0x00000000
T15FC 033:574.135 JLINK_ReadReg(FPS12)
T15FC 033:574.148 - 0.018ms returns 0x00000000
T15FC 033:574.163 JLINK_ReadReg(FPS13)
T15FC 033:574.175 - 0.018ms returns 0x00000000
T15FC 033:574.190 JLINK_ReadReg(FPS14)
T15FC 033:574.203 - 0.018ms returns 0x00000000
T15FC 033:574.218 JLINK_ReadReg(FPS15)
T15FC 033:574.231 - 0.018ms returns 0x00000000
T15FC 033:574.246 JLINK_ReadReg(FPS16)
T15FC 033:574.259 - 0.018ms returns 0x00000000
T15FC 033:574.274 JLINK_ReadReg(FPS17)
T15FC 033:574.286 - 0.019ms returns 0x00000000
T15FC 033:574.301 JLINK_ReadReg(FPS18)
T15FC 033:574.314 - 0.019ms returns 0x00000000
T15FC 033:574.329 JLINK_ReadReg(FPS19)
T15FC 033:574.342 - 0.018ms returns 0x00000000
T15FC 033:574.357 JLINK_ReadReg(FPS20)
T15FC 033:574.370 - 0.018ms returns 0x00000000
T15FC 033:574.385 JLINK_ReadReg(FPS21)
T15FC 033:574.397 - 0.019ms returns 0x00000000
T15FC 033:574.413 JLINK_ReadReg(FPS22)
T15FC 033:574.425 - 0.018ms returns 0x00000000
T15FC 033:574.440 JLINK_ReadReg(FPS23)
T15FC 033:574.453 - 0.018ms returns 0x00000000
T15FC 033:574.468 JLINK_ReadReg(FPS24)
T15FC 033:574.481 - 0.018ms returns 0x00000000
T15FC 033:574.496 JLINK_ReadReg(FPS25)
T15FC 033:574.509 - 0.019ms returns 0x00000000
T15FC 033:574.524 JLINK_ReadReg(FPS26)
T15FC 033:574.537 - 0.020ms returns 0x00000000
T15FC 033:574.563 JLINK_ReadReg(FPS27)
T15FC 033:574.582 - 0.027ms returns 0x00000000
T15FC 033:574.606 JLINK_ReadReg(FPS28)
T15FC 033:574.629 - 0.035ms returns 0x00000000
T15FC 033:574.655 JLINK_ReadReg(FPS29)
T15FC 033:574.693 - 0.049ms returns 0x00000000
T15FC 033:574.719 JLINK_ReadReg(FPS30)
T15FC 033:574.738 - 0.028ms returns 0x00000000
T15FC 033:574.760 JLINK_ReadReg(FPS31)
T15FC 033:574.778 - 0.028ms returns 0x00000000
T2BFC 033:574.964 JLINK_ReadMemEx(0x20000584, 0x4 Bytes, Flags = 0x02000000)
T2BFC 033:575.005   CPU_ReadMem(64 bytes @ 0x20000580)
T2BFC 033:576.351    -- Updating C cache (64 bytes @ 0x20000580)
T2BFC 033:576.380    -- Read from C cache (4 bytes @ 0x20000584)
T2BFC 033:576.400   Data:  FB 27 00 08
T2BFC 033:576.419 - 1.462ms returns 4 (0x4)
T2BFC 033:576.443 JLINK_ReadMemEx(0x20000578, 0x4 Bytes, Flags = 0x02000000)
T2BFC 033:576.462   CPU_ReadMem(64 bytes @ 0x20000540)
T2BFC 033:577.777    -- Updating C cache (64 bytes @ 0x20000540)
T2BFC 033:577.819    -- Read from C cache (4 bytes @ 0x20000578)
T2BFC 033:577.847   Data:  0C 00 00 20
T2BFC 033:577.872 - 1.443ms returns 4 (0x4)
T2BFC 033:577.915 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T2BFC 033:577.943    -- Read from C cache (4 bytes @ 0x2000057C)
T2BFC 033:577.974   Data:  5C 28 00 08
T2BFC 033:577.993 - 0.084ms returns 4 (0x4)
T2BFC 033:578.010 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T2BFC 033:578.026    -- Read from C cache (4 bytes @ 0x20000580)
T2BFC 033:578.044   Data:  00 00 00 00
T2BFC 033:578.062 - 0.058ms returns 4 (0x4)
T2BFC 033:578.077 JLINK_ReadMemEx(0x20000584, 0x4 Bytes, Flags = 0x02000000)
T2BFC 033:578.095    -- Read from C cache (4 bytes @ 0x20000584)
T2BFC 033:578.115   Data:  FB 27 00 08
T2BFC 033:578.133 - 0.061ms returns 4 (0x4)
T2BFC 033:578.168 JLINK_ReadMemEx(0x20000558, 0x10 Bytes, Flags = 0x02000000)
T2BFC 033:578.184    -- Read from C cache (16 bytes @ 0x20000558)
T2BFC 033:578.202   Data:  03 01 03 02 03 03 00 00 00 00 00 00 00 00 00 00
T2BFC 033:578.221 - 0.058ms returns 16 (0x10)
T2BFC 033:578.250 JLINK_ReadMemEx(0x20000568, 0x10 Bytes, Flags = 0x02000000)
T2BFC 033:578.265    -- Read from C cache (16 bytes @ 0x20000568)
T2BFC 033:578.284   Data:  00 00 00 00 00 00 00 00 00 00 00 00 01 00 00 00
T2BFC 033:578.302 - 0.057ms returns 16 (0x10)
T2BFC 033:578.354 JLINK_HasError()
T2BFC 033:578.371 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T2BFC 033:578.388   Data:  7F E2 CB 09
T2BFC 033:578.407   Debug reg: DWT_CYCCNT
T2BFC 033:578.425 - 0.059ms returns 1 (0x1)
T2BFC 033:580.004 JLINK_ReadMemEx(0x2000055F, 0x1 Bytes, Flags = 0x02000000)
T2BFC 033:580.034    -- Read from C cache (1 bytes @ 0x2000055F)
T2BFC 033:580.054   Data:  00
T2BFC 033:580.073 - 0.074ms returns 1 (0x1)
T2BFC 033:580.526 JLINK_ReadMemEx(0x2000055F, 0x1 Bytes, Flags = 0x02000000)
T2BFC 033:580.548    -- Read from C cache (1 bytes @ 0x2000055F)
T2BFC 033:580.567   Data:  00
T2BFC 033:580.585 - 0.065ms returns 1 (0x1)
T2BFC 033:582.204 JLINK_ReadMemEx(0x20000032, 0x2 Bytes, Flags = 0x02000000)
T2BFC 033:582.234   CPU_ReadMem(64 bytes @ 0x20000000)
T2BFC 033:583.779    -- Updating C cache (64 bytes @ 0x20000000)
T2BFC 033:583.807    -- Read from C cache (2 bytes @ 0x20000032)
T2BFC 033:583.826   Data:  04 03
T2BFC 033:583.844 - 1.647ms returns 2 (0x2)
T2BFC 033:583.868 JLINK_WriteMemEx(0x2000055F, 0x00000001 Bytes, Flags = 0x02000000)
T2BFC 033:583.882   Data:  04
T2BFC 033:583.908   CPU_WriteMem(1 bytes @ 0x2000055F)
T2BFC 033:584.970 - 1.115ms returns 0x1
T2BFC 033:585.302 JLINK_ReadMemEx(0x2000055E, 0x1 Bytes, Flags = 0x02000000)
T2BFC 033:585.327   CPU_ReadMem(64 bytes @ 0x20000540)
T2BFC 033:586.750    -- Updating C cache (64 bytes @ 0x20000540)
T2BFC 033:586.779    -- Read from C cache (1 bytes @ 0x2000055E)
T2BFC 033:586.799   Data:  00
T2BFC 033:586.818 - 1.522ms returns 1 (0x1)
T2BFC 033:588.494 JLINK_ReadMemEx(0x20000032, 0x2 Bytes, Flags = 0x02000000)
T2BFC 033:588.523    -- Read from C cache (2 bytes @ 0x20000032)
T2BFC 033:588.542   Data:  04 03
T2BFC 033:588.561 - 0.073ms returns 2 (0x2)
T2BFC 033:588.580 JLINK_WriteMemEx(0x2000055E, 0x00000001 Bytes, Flags = 0x02000000)
T2BFC 033:588.593   Data:  03
T2BFC 033:588.618   CPU_WriteMem(1 bytes @ 0x2000055E)
T2BFC 033:589.611 - 1.046ms returns 0x1
T15FC 034:286.620 JLINK_ReadMemEx(0x0800222A, 0x2 Bytes, Flags = 0x02000000)
T15FC 034:286.662    -- Read from flash cache (2 bytes @ 0x0800222A)
T15FC 034:286.682   Data:  08 28
T15FC 034:286.702 - 0.088ms returns 2 (0x2)
T15FC 034:286.720 JLINK_ReadMemEx(0x0800222C, 0x3C Bytes, Flags = 0x02000000)
T15FC 034:286.734    -- Read from flash cache (60 bytes @ 0x0800222C)
T15FC 034:286.753    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 034:286.775    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 034:286.810    -- Merging zombie BP[4]: 0x5D28 @ 0x08002258
T15FC 034:286.830   Data:  F4 DB 10 22 29 46 00 20 FE F7 59 F8 0A 20 FE F7 ...
T15FC 034:286.848 - 0.135ms returns 60 (0x3C)
T15FC 034:286.865 JLINK_ReadMemEx(0x0800222C, 0x2 Bytes, Flags = 0x02000000)
T15FC 034:286.879    -- Read from flash cache (2 bytes @ 0x0800222C)
T15FC 034:286.897   Data:  F4 DB
T15FC 034:286.915 - 0.056ms returns 2 (0x2)
T15FC 034:286.932 JLINK_ReadMemEx(0x0800222A, 0x2 Bytes, Flags = 0x02000000)
T15FC 034:286.945    -- Read from flash cache (2 bytes @ 0x0800222A)
T15FC 034:286.963   Data:  08 28
T15FC 034:286.981 - 0.055ms returns 2 (0x2)
T15FC 034:286.997 JLINK_HasError()
T15FC 034:287.015 JLINK_Step()
T15FC 034:287.679    -- Read from flash cache (2 bytes @ 0x0800222A)
T15FC 034:287.712   -- Simulated
T15FC 034:287.731 - 0.722ms returns 0
T15FC 034:287.750 JLINK_HasError()
T15FC 034:287.775 JLINK_ReadReg(R15 (PC))
T15FC 034:287.795 - 0.027ms returns 0x0800222C
T15FC 034:287.812 JLINK_ReadReg(XPSR)
T15FC 034:287.825 - 0.019ms returns 0x81000000
T15FC 034:287.849 JLINK_ReadMemEx(0x0800222C, 0x3C Bytes, Flags = 0x02000000)
T15FC 034:287.864    -- Read from flash cache (60 bytes @ 0x0800222C)
T15FC 034:287.882    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 034:287.901    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 034:287.919    -- Merging zombie BP[4]: 0x5D28 @ 0x08002258
T15FC 034:287.940   Data:  F4 DB 10 22 29 46 00 20 FE F7 59 F8 0A 20 FE F7 ...
T15FC 034:287.958 - 0.115ms returns 60 (0x3C)
T15FC 034:287.974 JLINK_ReadMemEx(0x0800222C, 0x2 Bytes, Flags = 0x02000000)
T15FC 034:287.987    -- Read from flash cache (2 bytes @ 0x0800222C)
T15FC 034:288.005   Data:  F4 DB
T15FC 034:288.024 - 0.056ms returns 2 (0x2)
T15FC 034:288.040 JLINK_ReadMemEx(0x0800222E, 0x2 Bytes, Flags = 0x02000000)
T15FC 034:288.053    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 034:288.071    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 034:288.089   Data:  10 22
T15FC 034:288.108 - 0.074ms returns 2 (0x2)
T15FC 034:288.123 JLINK_HasError()
T15FC 034:288.139 JLINK_Step()
T15FC 034:288.153    -- Read from flash cache (2 bytes @ 0x0800222C)
T15FC 034:288.172   -- Simulated
T15FC 034:288.190 - 0.057ms returns 0
T15FC 034:288.206 JLINK_HasError()
T15FC 034:288.221 JLINK_ReadReg(R15 (PC))
T15FC 034:288.234 - 0.019ms returns 0x08002218
T15FC 034:288.250 JLINK_ReadReg(XPSR)
T15FC 034:288.263 - 0.019ms returns 0x81000000
T15FC 034:288.280 JLINK_ReadMemEx(0x08002216, 0x2 Bytes, Flags = 0x02000000)
T15FC 034:288.294    -- Read from flash cache (2 bytes @ 0x08002216)
T15FC 034:288.312   Data:  0D 05
T15FC 034:288.330 - 0.056ms returns 2 (0x2)
T15FC 034:288.347 JLINK_ReadMemEx(0x08002218, 0x3C Bytes, Flags = 0x02000000)
T15FC 034:288.361    -- Read from flash cache (60 bytes @ 0x08002218)
T15FC 034:288.379    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 034:288.397    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 034:288.416   Data:  32 F8 10 10 0B 0A 05 F8 10 30 05 EB 40 03 40 1C ...
T15FC 034:288.434 - 0.092ms returns 60 (0x3C)
T15FC 034:288.449 JLINK_ReadMemEx(0x08002218, 0x2 Bytes, Flags = 0x02000000)
T15FC 034:288.463    -- Read from flash cache (2 bytes @ 0x08002218)
T15FC 034:288.481   Data:  32 F8
T15FC 034:288.499 - 0.056ms returns 2 (0x2)
T15FC 034:288.516 JLINK_ReadMemEx(0x08002218, 0x3C Bytes, Flags = 0x02000000)
T15FC 034:288.529    -- Read from flash cache (60 bytes @ 0x08002218)
T15FC 034:288.547    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 034:288.565    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 034:288.584   Data:  32 F8 10 10 0B 0A 05 F8 10 30 05 EB 40 03 40 1C ...
T15FC 034:288.602 - 0.092ms returns 60 (0x3C)
T15FC 034:288.617 JLINK_ReadMemEx(0x08002218, 0x2 Bytes, Flags = 0x02000000)
T15FC 034:288.631    -- Read from flash cache (2 bytes @ 0x08002218)
T15FC 034:288.649   Data:  32 F8
T15FC 034:288.667 - 0.056ms returns 2 (0x2)
T15FC 034:288.682 JLINK_ReadMemEx(0x0800221A, 0x2 Bytes, Flags = 0x02000000)
T15FC 034:288.696    -- Read from flash cache (2 bytes @ 0x0800221A)
T15FC 034:288.714   Data:  10 10
T15FC 034:288.733 - 0.056ms returns 2 (0x2)
T15FC 034:288.748 JLINK_HasError()
T15FC 034:288.764 JLINK_Step()
T15FC 034:288.787    -- Read from flash cache (2 bytes @ 0x08002218)
T15FC 034:288.818    -- Read from flash cache (2 bytes @ 0x0800221A)
T15FC 034:288.846   -- Not simulated
T15FC 034:288.892   CPU_WriteMem(4 bytes @ 0xE0001004)
T15FC 034:296.102 - 7.365ms returns 0
T15FC 034:296.148 JLINK_HasError()
T15FC 034:296.166 JLINK_ReadReg(R15 (PC))
T15FC 034:296.183 - 0.022ms returns 0x0800221C
T15FC 034:296.200 JLINK_ReadReg(XPSR)
T15FC 034:296.213 - 0.019ms returns 0x81000000
T15FC 034:296.241 JLINK_ReadMemEx(0x0800221A, 0x2 Bytes, Flags = 0x02000000)
T15FC 034:296.257    -- Read from flash cache (2 bytes @ 0x0800221A)
T15FC 034:296.276   Data:  10 10
T15FC 034:296.295 - 0.108ms returns 2 (0x2)
T15FC 034:296.365 JLINK_ReadMemEx(0x0800221C, 0x3C Bytes, Flags = 0x02000000)
T15FC 034:296.379    -- Read from flash cache (60 bytes @ 0x0800221C)
T15FC 034:296.398    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 034:296.417    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 034:296.470   Data:  0B 0A 05 F8 10 30 05 EB 40 03 40 1C 59 70 08 28 ...
T15FC 034:296.488 - 0.129ms returns 60 (0x3C)
T15FC 034:296.505 JLINK_ReadMemEx(0x0800221C, 0x2 Bytes, Flags = 0x02000000)
T15FC 034:296.519    -- Read from flash cache (2 bytes @ 0x0800221C)
T15FC 034:296.537   Data:  0B 0A
T15FC 034:296.555 - 0.055ms returns 2 (0x2)
T15FC 034:296.572 JLINK_ReadMemEx(0x0800221C, 0x3C Bytes, Flags = 0x02000000)
T15FC 034:296.586    -- Read from flash cache (60 bytes @ 0x0800221C)
T15FC 034:296.604    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 034:296.622    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 034:296.641   Data:  0B 0A 05 F8 10 30 05 EB 40 03 40 1C 59 70 08 28 ...
T15FC 034:296.658 - 0.092ms returns 60 (0x3C)
T15FC 034:296.675 JLINK_ReadMemEx(0x0800221C, 0x2 Bytes, Flags = 0x02000000)
T15FC 034:296.689    -- Read from flash cache (2 bytes @ 0x0800221C)
T15FC 034:296.707   Data:  0B 0A
T15FC 034:296.725 - 0.055ms returns 2 (0x2)
T15FC 034:296.743 JLINK_ReadMemEx(0x0800221E, 0x2 Bytes, Flags = 0x02000000)
T15FC 034:296.770    -- Read from flash cache (2 bytes @ 0x0800221E)
T15FC 034:296.793   Data:  05 F8
T15FC 034:296.811 - 0.074ms returns 2 (0x2)
T15FC 034:296.830 JLINK_HasError()
T15FC 034:296.847 JLINK_Step()
T15FC 034:296.862    -- Read from flash cache (2 bytes @ 0x0800221C)
T15FC 034:296.882   CPU_ReadMem(4 bytes @ 0xE0001004)
T15FC 034:297.767   -- Simulated
T15FC 034:297.799 - 0.960ms returns 0
T15FC 034:297.826 JLINK_HasError()
T15FC 034:297.857 JLINK_ReadReg(R15 (PC))
T15FC 034:297.882 - 0.032ms returns 0x0800221E
T15FC 034:297.902 JLINK_ReadReg(XPSR)
T15FC 034:297.931 - 0.038ms returns 0x01000000
T15FC 034:297.958 JLINK_ReadMemEx(0x0800221E, 0x2 Bytes, Flags = 0x02000000)
T15FC 034:297.974    -- Read from flash cache (2 bytes @ 0x0800221E)
T15FC 034:297.992   Data:  05 F8
T15FC 034:298.011 - 0.059ms returns 2 (0x2)
T15FC 034:298.028 JLINK_ReadMemEx(0x08002220, 0x3C Bytes, Flags = 0x02000000)
T15FC 034:298.042    -- Read from flash cache (60 bytes @ 0x08002220)
T15FC 034:298.060    -- Merging zombie BP[1]: 0x2210 @ 0x0800222E
T15FC 034:298.079    -- Merging zombie BP[3]: 0x2210 @ 0x0800223E
T15FC 034:298.097    -- Merging zombie BP[4]: 0x5D28 @ 0x08002258
T15FC 034:298.115   Data:  10 30 05 EB 40 03 40 1C 59 70 08 28 F4 DB 10 22 ...
T15FC 034:298.133 - 0.111ms returns 60 (0x3C)
T15FC 034:298.150 JLINK_ReadMemEx(0x08002220, 0x2 Bytes, Flags = 0x02000000)
T15FC 034:298.164    -- Read from flash cache (2 bytes @ 0x08002220)
T15FC 034:298.182   Data:  10 30
T15FC 034:298.200 - 0.056ms returns 2 (0x2)
T15FC 034:298.218 JLINK_HasError()
T15FC 034:298.233 JLINK_Step()
T15FC 034:298.247    -- Read from flash cache (2 bytes @ 0x0800221E)
T15FC 034:298.267    -- Read from flash cache (2 bytes @ 0x08002220)
T15FC 034:298.285   -- Not simulated
T15FC 034:298.313   CPU_WriteMem(4 bytes @ 0xE0001004)
T15FC 034:305.603 - 7.391ms returns 0
T15FC 034:305.641 JLINK_HasError()
T15FC 034:305.659 JLINK_ReadReg(R15 (PC))
T15FC 034:305.674 - 0.022ms returns 0x08002222
T15FC 034:305.692 JLINK_ReadReg(XPSR)
T15FC 034:305.706 - 0.019ms returns 0x01000000
T15FC 034:305.910 JLINK_HasError()
T15FC 034:305.936 JLINK_ReadReg(R0)
T15FC 034:305.952 - 0.022ms returns 0x00000003
T15FC 034:305.968 JLINK_ReadReg(R1)
T15FC 034:305.981 - 0.019ms returns 0x00000304
T15FC 034:305.997 JLINK_ReadReg(R2)
T15FC 034:306.010 - 0.019ms returns 0x2000002C
T15FC 034:306.025 JLINK_ReadReg(R3)
T15FC 034:306.038 - 0.019ms returns 0x00000003
T15FC 034:306.053 JLINK_ReadReg(R4)
T15FC 034:306.066 - 0.019ms returns 0x2000000C
T15FC 034:306.081 JLINK_ReadReg(R5)
T15FC 034:306.094 - 0.019ms returns 0x20000558
T15FC 034:306.109 JLINK_ReadReg(R6)
T15FC 034:306.122 - 0.076ms returns 0x00000000
T15FC 034:306.197 JLINK_ReadReg(R7)
T15FC 034:306.211 - 0.020ms returns 0x00000000
T15FC 034:306.226 JLINK_ReadReg(R8)
T15FC 034:306.239 - 0.019ms returns 0x00000000
T15FC 034:306.254 JLINK_ReadReg(R9)
T15FC 034:306.267 - 0.018ms returns 0x00000000
T15FC 034:306.282 JLINK_ReadReg(R10)
T15FC 034:306.294 - 0.018ms returns 0x00000000
T15FC 034:306.310 JLINK_ReadReg(R11)
T15FC 034:306.322 - 0.018ms returns 0x00000000
T15FC 034:306.337 JLINK_ReadReg(R12)
T15FC 034:306.350 - 0.018ms returns 0x0000000E
T15FC 034:306.365 JLINK_ReadReg(R13 (SP))
T15FC 034:306.378 - 0.019ms returns 0x20000558
T15FC 034:306.394 JLINK_ReadReg(R14)
T15FC 034:306.406 - 0.019ms returns 0x080027FB
T15FC 034:306.421 JLINK_ReadReg(R15 (PC))
T15FC 034:306.434 - 0.019ms returns 0x08002222
T15FC 034:306.449 JLINK_ReadReg(XPSR)
T15FC 034:306.462 - 0.018ms returns 0x01000000
T15FC 034:306.477 JLINK_ReadReg(MSP)
T15FC 034:306.490 - 0.019ms returns 0x20000558
T15FC 034:306.505 JLINK_ReadReg(PSP)
T15FC 034:306.518 - 0.018ms returns 0x00000000
T15FC 034:306.533 JLINK_ReadReg(CFBP)
T15FC 034:306.545 - 0.018ms returns 0x00000000
T15FC 034:306.561 JLINK_ReadReg(FPSCR)
T15FC 034:312.320 - 5.784ms returns 0x00000000
T15FC 034:312.360 JLINK_ReadReg(FPS0)
T15FC 034:312.376 - 0.023ms returns 0x00000000
T15FC 034:312.392 JLINK_ReadReg(FPS1)
T15FC 034:312.405 - 0.019ms returns 0x00000000
T15FC 034:312.420 JLINK_ReadReg(FPS2)
T15FC 034:312.433 - 0.019ms returns 0x00000000
T15FC 034:312.448 JLINK_ReadReg(FPS3)
T15FC 034:312.461 - 0.019ms returns 0x00000000
T15FC 034:312.476 JLINK_ReadReg(FPS4)
T15FC 034:312.489 - 0.019ms returns 0x00000000
T15FC 034:312.504 JLINK_ReadReg(FPS5)
T15FC 034:312.517 - 0.019ms returns 0x00000000
T15FC 034:312.532 JLINK_ReadReg(FPS6)
T15FC 034:312.545 - 0.019ms returns 0x00000000
T15FC 034:312.560 JLINK_ReadReg(FPS7)
T15FC 034:312.573 - 0.019ms returns 0x00000000
T15FC 034:312.588 JLINK_ReadReg(FPS8)
T15FC 034:312.601 - 0.018ms returns 0x00000000
T15FC 034:312.616 JLINK_ReadReg(FPS9)
T15FC 034:312.628 - 0.018ms returns 0x00000000
T15FC 034:312.643 JLINK_ReadReg(FPS10)
T15FC 034:312.656 - 0.018ms returns 0x00000000
T15FC 034:312.671 JLINK_ReadReg(FPS11)
T15FC 034:312.684 - 0.018ms returns 0x00000000
T15FC 034:312.699 JLINK_ReadReg(FPS12)
T15FC 034:312.712 - 0.018ms returns 0x00000000
T15FC 034:312.727 JLINK_ReadReg(FPS13)
T15FC 034:312.743 - 0.022ms returns 0x00000000
T15FC 034:312.758 JLINK_ReadReg(FPS14)
T15FC 034:312.771 - 0.018ms returns 0x00000000
T15FC 034:312.786 JLINK_ReadReg(FPS15)
T15FC 034:312.798 - 0.018ms returns 0x00000000
T15FC 034:312.813 JLINK_ReadReg(FPS16)
T15FC 034:312.826 - 0.018ms returns 0x00000000
T15FC 034:312.841 JLINK_ReadReg(FPS17)
T15FC 034:312.854 - 0.019ms returns 0x00000000
T15FC 034:312.869 JLINK_ReadReg(FPS18)
T15FC 034:312.881 - 0.018ms returns 0x00000000
T15FC 034:312.897 JLINK_ReadReg(FPS19)
T15FC 034:312.909 - 0.018ms returns 0x00000000
T15FC 034:312.924 JLINK_ReadReg(FPS20)
T15FC 034:312.937 - 0.018ms returns 0x00000000
T15FC 034:312.952 JLINK_ReadReg(FPS21)
T15FC 034:312.965 - 0.019ms returns 0x00000000
T15FC 034:312.980 JLINK_ReadReg(FPS22)
T15FC 034:312.993 - 0.019ms returns 0x00000000
T15FC 034:313.008 JLINK_ReadReg(FPS23)
T15FC 034:313.020 - 0.019ms returns 0x00000000
T15FC 034:313.036 JLINK_ReadReg(FPS24)
T15FC 034:313.048 - 0.018ms returns 0x00000000
T15FC 034:313.063 JLINK_ReadReg(FPS25)
T15FC 034:313.076 - 0.018ms returns 0x00000000
T15FC 034:313.091 JLINK_ReadReg(FPS26)
T15FC 034:313.104 - 0.018ms returns 0x00000000
T15FC 034:313.119 JLINK_ReadReg(FPS27)
T15FC 034:313.131 - 0.018ms returns 0x00000000
T15FC 034:313.146 JLINK_ReadReg(FPS28)
T15FC 034:313.159 - 0.018ms returns 0x00000000
T15FC 034:313.174 JLINK_ReadReg(FPS29)
T15FC 034:313.187 - 0.018ms returns 0x00000000
T15FC 034:313.202 JLINK_ReadReg(FPS30)
T15FC 034:313.215 - 0.018ms returns 0x00000000
T15FC 034:313.230 JLINK_ReadReg(FPS31)
T15FC 034:313.242 - 0.018ms returns 0x00000000
T2BFC 034:313.472 JLINK_ReadMemEx(0x20000584, 0x4 Bytes, Flags = 0x02000000)
T2BFC 034:313.509   CPU_ReadMem(64 bytes @ 0x20000580)
T2BFC 034:315.071    -- Updating C cache (64 bytes @ 0x20000580)
T2BFC 034:315.098    -- Read from C cache (4 bytes @ 0x20000584)
T2BFC 034:315.118   Data:  FB 27 00 08
T2BFC 034:315.136 - 1.671ms returns 4 (0x4)
T2BFC 034:315.159 JLINK_ReadMemEx(0x20000578, 0x4 Bytes, Flags = 0x02000000)
T2BFC 034:315.177   CPU_ReadMem(64 bytes @ 0x20000540)
T2BFC 034:316.746    -- Updating C cache (64 bytes @ 0x20000540)
T2BFC 034:316.785    -- Read from C cache (4 bytes @ 0x20000578)
T2BFC 034:316.804   Data:  0C 00 00 20
T2BFC 034:316.823 - 1.670ms returns 4 (0x4)
T2BFC 034:316.842 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T2BFC 034:316.859    -- Read from C cache (4 bytes @ 0x2000057C)
T2BFC 034:316.878   Data:  5C 28 00 08
T2BFC 034:316.896 - 0.060ms returns 4 (0x4)
T2BFC 034:316.911 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T2BFC 034:316.925    -- Read from C cache (4 bytes @ 0x20000580)
T2BFC 034:316.944   Data:  00 00 00 00
T2BFC 034:316.962 - 0.056ms returns 4 (0x4)
T2BFC 034:316.977 JLINK_ReadMemEx(0x20000584, 0x4 Bytes, Flags = 0x02000000)
T2BFC 034:316.991    -- Read from C cache (4 bytes @ 0x20000584)
T2BFC 034:317.009   Data:  FB 27 00 08
T2BFC 034:317.027 - 0.056ms returns 4 (0x4)
T2BFC 034:317.058 JLINK_ReadMemEx(0x20000558, 0x10 Bytes, Flags = 0x02000000)
T2BFC 034:317.073    -- Read from C cache (16 bytes @ 0x20000558)
T2BFC 034:317.094   Data:  03 01 03 02 03 03 03 04 00 00 00 00 00 00 00 00
T2BFC 034:317.118 - 0.066ms returns 16 (0x10)
T2BFC 034:317.152 JLINK_ReadMemEx(0x20000568, 0x10 Bytes, Flags = 0x02000000)
T2BFC 034:317.167    -- Read from C cache (16 bytes @ 0x20000568)
T2BFC 034:317.186   Data:  00 00 00 00 00 00 00 00 00 00 00 00 01 00 00 00
T2BFC 034:317.204 - 0.058ms returns 16 (0x10)
T2BFC 034:317.240 JLINK_HasError()
T2BFC 034:317.257 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T2BFC 034:317.273   CPU_ReadMem(4 bytes @ 0xE0001004)
T2BFC 034:318.278   Data:  86 E2 CB 09
T2BFC 034:318.305   Debug reg: DWT_CYCCNT
T2BFC 034:318.323 - 1.072ms returns 1 (0x1)
T2BFC 034:319.892 JLINK_ReadMemEx(0x2000055F, 0x1 Bytes, Flags = 0x02000000)
T2BFC 034:319.920    -- Read from C cache (1 bytes @ 0x2000055F)
T2BFC 034:319.940   Data:  04
T2BFC 034:319.958 - 0.073ms returns 1 (0x1)
T2BFC 034:320.415 JLINK_ReadMemEx(0x2000055F, 0x1 Bytes, Flags = 0x02000000)
T2BFC 034:320.439    -- Read from C cache (1 bytes @ 0x2000055F)
T2BFC 034:320.458   Data:  04
T2BFC 034:320.476 - 0.067ms returns 1 (0x1)
T2BFC 034:322.147 JLINK_ReadMemEx(0x20000032, 0x2 Bytes, Flags = 0x02000000)
T2BFC 034:322.182   CPU_ReadMem(64 bytes @ 0x20000000)
T2BFC 034:323.727    -- Updating C cache (64 bytes @ 0x20000000)
T2BFC 034:323.760    -- Read from C cache (2 bytes @ 0x20000032)
T2BFC 034:323.779   Data:  04 03
T2BFC 034:323.798 - 1.658ms returns 2 (0x2)
T2BFC 034:323.828 JLINK_WriteMemEx(0x2000055F, 0x00000001 Bytes, Flags = 0x02000000)
T2BFC 034:323.842   Data:  04
T2BFC 034:323.867   CPU_WriteMem(1 bytes @ 0x2000055F)
T2BFC 034:324.723 - 0.926ms returns 0x1
T2BFC 034:325.101 JLINK_ReadMemEx(0x2000055E, 0x1 Bytes, Flags = 0x02000000)
T2BFC 034:325.129   CPU_ReadMem(64 bytes @ 0x20000540)
T2BFC 034:326.717    -- Updating C cache (64 bytes @ 0x20000540)
T2BFC 034:326.750    -- Read from C cache (1 bytes @ 0x2000055E)
T2BFC 034:326.769   Data:  03
T2BFC 034:326.788 - 1.693ms returns 1 (0x1)
T2BFC 034:328.515 JLINK_ReadMemEx(0x20000032, 0x2 Bytes, Flags = 0x02000000)
T2BFC 034:328.543    -- Read from C cache (2 bytes @ 0x20000032)
T2BFC 034:328.563   Data:  04 03
T2BFC 034:328.581 - 0.073ms returns 2 (0x2)
T2BFC 034:328.602 JLINK_WriteMemEx(0x2000055E, 0x00000001 Bytes, Flags = 0x02000000)
T2BFC 034:328.615   Data:  03
T2BFC 034:328.641   CPU_WriteMem(1 bytes @ 0x2000055E)
T2BFC 034:329.585 - 0.998ms returns 0x1
T1BF0 142:252.361   
  ***** Error: Connection to emulator lost!
T2BFC 247:302.310 JLINK_HasError()
T2BFC 247:309.451 JLINK_Close()
T2BFC 247:317.819 - 8.396ms
T2BFC 247:317.858   
T2BFC 247:317.871   Closed
