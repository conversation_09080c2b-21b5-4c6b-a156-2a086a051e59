--cpu=Cortex-M4.fp.sp
"bsp_can_test\startup_stm32f407xx.o"
"bsp_can_test\main.o"
"bsp_can_test\gpio.o"
"bsp_can_test\can.o"
"bsp_can_test\i2c.o"
"bsp_can_test\tim.o"
"bsp_can_test\usart.o"
"bsp_can_test\stm32f4xx_it.o"
"bsp_can_test\stm32f4xx_hal_msp.o"
"bsp_can_test\stm32f4xx_hal_can.o"
"bsp_can_test\stm32f4xx_hal_rcc.o"
"bsp_can_test\stm32f4xx_hal_rcc_ex.o"
"bsp_can_test\stm32f4xx_hal_flash.o"
"bsp_can_test\stm32f4xx_hal_flash_ex.o"
"bsp_can_test\stm32f4xx_hal_flash_ramfunc.o"
"bsp_can_test\stm32f4xx_hal_gpio.o"
"bsp_can_test\stm32f4xx_hal_dma_ex.o"
"bsp_can_test\stm32f4xx_hal_dma.o"
"bsp_can_test\stm32f4xx_hal_pwr.o"
"bsp_can_test\stm32f4xx_hal_pwr_ex.o"
"bsp_can_test\stm32f4xx_hal_cortex.o"
"bsp_can_test\stm32f4xx_hal.o"
"bsp_can_test\stm32f4xx_hal_exti.o"
"bsp_can_test\stm32f4xx_hal_i2c.o"
"bsp_can_test\stm32f4xx_hal_i2c_ex.o"
"bsp_can_test\stm32f4xx_hal_tim.o"
"bsp_can_test\stm32f4xx_hal_tim_ex.o"
"bsp_can_test\stm32f4xx_hal_uart.o"
"bsp_can_test\system_stm32f4xx.o"
--library_type=microlib --strict --scatter "BSP_CAN_test\BSP_CAN_test.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "BSP_CAN_test.map" -o BSP_CAN_test\BSP_CAN_test.axf