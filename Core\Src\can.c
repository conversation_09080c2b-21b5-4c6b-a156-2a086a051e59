/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    can.c
  * @brief   This file provides code for the configuration
  *          of the CAN instances.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "can.h"

/* USER CODE BEGIN 0 */
#include "usart.h"
#include "tim.h"
#include "string.h"
#include "i2c.h"
#include "stdio.h"
#include "stdbool.h"
// 推进器CAN ID数组
uint8_t propulsor_ids[NUM_PROPULSORS] = {0};
/* USER CODE END 0 */

CAN_HandleTypeDef hcan1;
CAN_HandleTypeDef hcan2;

/* CAN1 init function */
void MX_CAN1_Init(void)
{

  /* USER CODE BEGIN CAN1_Init 0 */

  /* USER CODE END CAN1_Init 0 */

  /* USER CODE BEGIN CAN1_Init 1 */

  /* USER CODE END CAN1_Init 1 */
  hcan1.Instance = CAN1;
  hcan1.Init.Prescaler = 9;
  hcan1.Init.Mode = CAN_MODE_NORMAL;
  hcan1.Init.SyncJumpWidth = CAN_SJW_1TQ;
  hcan1.Init.TimeSeg1 = CAN_BS1_4TQ;
  hcan1.Init.TimeSeg2 = CAN_BS2_3TQ;
  hcan1.Init.TimeTriggeredMode = DISABLE;
  hcan1.Init.AutoBusOff = ENABLE;
  hcan1.Init.AutoWakeUp = ENABLE;
  hcan1.Init.AutoRetransmission = DISABLE;
  hcan1.Init.ReceiveFifoLocked = DISABLE;
  hcan1.Init.TransmitFifoPriority = DISABLE;
  if (HAL_CAN_Init(&hcan1) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN CAN1_Init 2 */

  /* USER CODE END CAN1_Init 2 */

}
/* CAN2 init function */
void MX_CAN2_Init(void)
{

  /* USER CODE BEGIN CAN2_Init 0 */

  /* USER CODE END CAN2_Init 0 */

  /* USER CODE BEGIN CAN2_Init 1 */

  /* USER CODE END CAN2_Init 1 */
  hcan2.Instance = CAN2;
  hcan2.Init.Prescaler = 9;
  hcan2.Init.Mode = CAN_MODE_NORMAL;
  hcan2.Init.SyncJumpWidth = CAN_SJW_1TQ;
  hcan2.Init.TimeSeg1 = CAN_BS1_4TQ;
  hcan2.Init.TimeSeg2 = CAN_BS2_3TQ;
  hcan2.Init.TimeTriggeredMode = DISABLE;
  hcan2.Init.AutoBusOff = ENABLE;
  hcan2.Init.AutoWakeUp = ENABLE;
  hcan2.Init.AutoRetransmission = DISABLE;
  hcan2.Init.ReceiveFifoLocked = DISABLE;
  hcan2.Init.TransmitFifoPriority = DISABLE;
  if (HAL_CAN_Init(&hcan2) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN CAN2_Init 2 */

  /* USER CODE END CAN2_Init 2 */

}

static uint32_t HAL_RCC_CAN1_CLK_ENABLED=0;

void HAL_CAN_MspInit(CAN_HandleTypeDef* canHandle)
{

  GPIO_InitTypeDef GPIO_InitStruct = {0};
  if(canHandle->Instance==CAN1)
  {
  /* USER CODE BEGIN CAN1_MspInit 0 */

  /* USER CODE END CAN1_MspInit 0 */
    /* CAN1 clock enable */
    HAL_RCC_CAN1_CLK_ENABLED++;
    if(HAL_RCC_CAN1_CLK_ENABLED==1){
      __HAL_RCC_CAN1_CLK_ENABLE();
    }

    __HAL_RCC_GPIOA_CLK_ENABLE();
    /**CAN1 GPIO Configuration
    PA11     ------> CAN1_RX
    PA12     ------> CAN1_TX
    */
    GPIO_InitStruct.Pin = GPIO_PIN_11|GPIO_PIN_12;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF9_CAN1;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    /* CAN1 interrupt Init */
    HAL_NVIC_SetPriority(CAN1_RX0_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(CAN1_RX0_IRQn);
  /* USER CODE BEGIN CAN1_MspInit 1 */

  /* USER CODE END CAN1_MspInit 1 */
  }
  else if(canHandle->Instance==CAN2)
  {
  /* USER CODE BEGIN CAN2_MspInit 0 */

  /* USER CODE END CAN2_MspInit 0 */
    /* CAN2 clock enable */
    __HAL_RCC_CAN2_CLK_ENABLE();
    HAL_RCC_CAN1_CLK_ENABLED++;
    if(HAL_RCC_CAN1_CLK_ENABLED==1){
      __HAL_RCC_CAN1_CLK_ENABLE();
    }

    __HAL_RCC_GPIOB_CLK_ENABLE();
    /**CAN2 GPIO Configuration
    PB12     ------> CAN2_RX
    PB13     ------> CAN2_TX
    */
    GPIO_InitStruct.Pin = GPIO_PIN_12|GPIO_PIN_13;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF9_CAN2;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    /* CAN2 interrupt Init */
    HAL_NVIC_SetPriority(CAN2_RX1_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(CAN2_RX1_IRQn);
  /* USER CODE BEGIN CAN2_MspInit 1 */

  /* USER CODE END CAN2_MspInit 1 */
  }
}

void HAL_CAN_MspDeInit(CAN_HandleTypeDef* canHandle)
{

  if(canHandle->Instance==CAN1)
  {
  /* USER CODE BEGIN CAN1_MspDeInit 0 */

  /* USER CODE END CAN1_MspDeInit 0 */
    /* Peripheral clock disable */
    HAL_RCC_CAN1_CLK_ENABLED--;
    if(HAL_RCC_CAN1_CLK_ENABLED==0){
      __HAL_RCC_CAN1_CLK_DISABLE();
    }

    /**CAN1 GPIO Configuration
    PA11     ------> CAN1_RX
    PA12     ------> CAN1_TX
    */
    HAL_GPIO_DeInit(GPIOA, GPIO_PIN_11|GPIO_PIN_12);

    /* CAN1 interrupt Deinit */
    HAL_NVIC_DisableIRQ(CAN1_RX0_IRQn);
  /* USER CODE BEGIN CAN1_MspDeInit 1 */

  /* USER CODE END CAN1_MspDeInit 1 */
  }
  else if(canHandle->Instance==CAN2)
  {
  /* USER CODE BEGIN CAN2_MspDeInit 0 */

  /* USER CODE END CAN2_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_CAN2_CLK_DISABLE();
    HAL_RCC_CAN1_CLK_ENABLED--;
    if(HAL_RCC_CAN1_CLK_ENABLED==0){
      __HAL_RCC_CAN1_CLK_DISABLE();
    }

    /**CAN2 GPIO Configuration
    PB12     ------> CAN2_RX
    PB13     ------> CAN2_TX
    */
    HAL_GPIO_DeInit(GPIOB, GPIO_PIN_12|GPIO_PIN_13);

    /* CAN2 interrupt Deinit */
    HAL_NVIC_DisableIRQ(CAN2_RX1_IRQn);
  /* USER CODE BEGIN CAN2_MspDeInit 1 */

  /* USER CODE END CAN2_MspDeInit 1 */
  }
}

/* USER CODE BEGIN 1 */
//------------------CAN1配置--------------------------------//
void CAN1_Config(void)
{	
 
	CAN_FilterTypeDef  sFilterConfig;
 
  /*配置CAN过滤器*/
  sFilterConfig.FilterBank = 0;                     //过滤器0
  sFilterConfig.FilterMode = CAN_FILTERMODE_IDMASK;
  sFilterConfig.FilterScale = CAN_FILTERSCALE_32BIT;
  sFilterConfig.FilterIdHigh = 0x0000;              //32位ID
  sFilterConfig.FilterIdLow = 0x0000;
  sFilterConfig.FilterMaskIdHigh = 0x0000;          //32位MASK
  sFilterConfig.FilterMaskIdLow = 0x0000;
  sFilterConfig.FilterFIFOAssignment = CAN_RX_FIFO0;//过滤器0关联到FIFO0
  sFilterConfig.FilterActivation = ENABLE;          //激活滤波器0
  sFilterConfig.SlaveStartFilterBank = 14;
	
	
 if(HAL_CAN_ConfigFilter(&hcan1,&sFilterConfig) != HAL_OK)//初始化过滤器
 {
  Error_Handler();
 }
 if(HAL_CAN_Start(&hcan1) != HAL_OK)//打开can
 {
  Error_Handler();
 }
 if(HAL_CAN_ActivateNotification(&hcan1,CAN_IT_RX_FIFO0_MSG_PENDING) != HAL_OK)//开启接受邮邮箱0挂起中断
 {
  Error_Handler();
 }
 
}

//------------------CAN2配置--------------------------------//
void CAN2_Config(void)
{	
 
	CAN_FilterTypeDef  sFilterConfig;
 
  /*配置CAN过滤器*/
  sFilterConfig.FilterBank = 0;                     //过滤器0
  sFilterConfig.FilterMode = CAN_FILTERMODE_IDMASK;
  sFilterConfig.FilterScale = CAN_FILTERSCALE_32BIT;
  sFilterConfig.FilterIdHigh = 0x0000;              //32位ID
  sFilterConfig.FilterIdLow = 0x0000;
  sFilterConfig.FilterMaskIdHigh = 0x0000;          //32位MASK
  sFilterConfig.FilterMaskIdLow = 0x0000;
  sFilterConfig.FilterFIFOAssignment = CAN_RX_FIFO0;//过滤器0关联到FIFO0
  sFilterConfig.FilterActivation = ENABLE;          //激活滤波器0
  sFilterConfig.SlaveStartFilterBank = 14;
	
	
 if(HAL_CAN_ConfigFilter(&hcan2,&sFilterConfig) != HAL_OK)//初始化过滤器
 {
  Error_Handler();
 }
 if(HAL_CAN_Start(&hcan2) != HAL_OK)//打开can
 {
  Error_Handler();
 }
 if(HAL_CAN_ActivateNotification(&hcan2,CAN_IT_RX_FIFO0_MSG_PENDING) != HAL_OK)//开启接受邮邮箱0挂起中断
 {
  Error_Handler();
 }
 
}

//-------------------------------CAN模拟主控发送数据的函数-----------------------// 
void SendTestCanMessage(void)
{
    // 构建一个2字节的测试消息
    uint8_t testData[2];
    testData[0] = 0x01; // 0x01是前进命令
    testData[1] = 0x7F; // 速度值 (127)
	
    // 设置发送的消息ID（标准ID格式）
    uint32_t testId = 0x31B; 

    // 发送CAN消息
    CAN_SendMessage(testId, testData, 2);

    // 延时一段时间确保消息被发送出去
    //HAL_Delay(100);
}
//---------------------------------------------------------------------------//


/* 函数名称：从两个字节中提取推进器命令
	*函数参数：uint8_t high_byte, uint8_t low_byte
	*函数返回值：结构体
	*函数作用：从接收到的两个字节中提取速度和方向信息
*/ 
PropulsorCommand ExtractPropulsorCommand(uint8_t high_byte, uint8_t low_byte)
{
    // 提取推进器命令
    PropulsorCommand cmd;
    // 将两个字节合并为一个16位整数
    cmd.raw_speed = high_byte;
    // 判断方向
    cmd.direction = (high_byte & 0x80) ? DIRECTION_REVERSE : DIRECTION_FORWARD;
    // 提取速度值
    cmd.speed = low_byte;
    return cmd;
}

/* 函数名称：准备发送缓冲区
	*函数参数：PropulsorCommand cmd, uint8_t *buffer
	*函数返回值：无返回值
	*函数作用：将命令打包成标准格式的8字节消息
*/ 
void PrepareCommandBuffer(PropulsorCommand cmd, uint8_t *buffer)
{
    // 填充命令头：54 43 00 00
    buffer[0] = CMD_HEADER_1;   // 0x54
    buffer[1] = CMD_HEADER_2;   // 0x43
    buffer[2] = CMD_RESERVED_1; // 0x00
    buffer[3] = CMD_RESERVED_2; // 0x00

    // 填充方向和速度：根据方向填充后4字节
    if (cmd.direction == DIRECTION_FORWARD) {
        // 正向：00 00 [速度高字节] [速度低字节]
        buffer[4] = 0x00;
        buffer[5] = 0x00;
        buffer[6] = (cmd.speed >> 8) & 0xFF;  // 速度高字节
        buffer[7] = cmd.speed & 0xFF;         // 速度低字节
    } else {
        // 反向：FF FF [速度高字节] [速度低字节]
        buffer[4] = CMD_REVERSE_BYTE; // 0xFF
        buffer[5] = CMD_REVERSE_BYTE; // 0xFF
        buffer[6] = (cmd.speed >> 8) & 0xFF;  // 速度高字节
        buffer[7] = cmd.speed & 0xFF;         // 速度低字节
    }
}

/* 函数名称：发送推进器命令
	*函数参数：uint32_t can_id, PropulsorCommand cmd
	*函数返回值：无返回值
	*函数作用：用来将处理好的数据发送出去
*/ 
void SendPropulsorCommand(uint32_t can_id, PropulsorCommand cmd)
{
    uint8_t buffer[8];
    // 准备命令缓冲区
    PrepareCommandBuffer(cmd, buffer);
    // 发送CAN消息
    CAN_SendMessage(can_id, buffer, 8);
}

/* 函数名称：处理推进器命令
	*函数参数：uint8_t *data, uint8_t start_index, uint8_t count
	*函数返回值：无返回值
	*函数作用：用来处理数据
*/ 
void ProcessPropulsorCommands(uint8_t *data, uint8_t start_index, uint8_t count)
{
    for (uint8_t i = 0; i < count; i++)
    {
        // 提取推进器命令
        PropulsorCommand cmd = ExtractPropulsorCommand(data[i*2], data[i*2 + 1]);
        // 发送推进器命令
        SendPropulsorCommand(CAN_ID[start_index + i], cmd);
        // 短暂延时确保消息发送
        //HAL_Delay(10);  
    }
}
//-------------------------------CAN中断程序-----------------------------------//
// 接收：CAN总线 -> 中断处理 -> 消息解析 -> 命令提取 -> 执行操作
// 发送：命令生成 -> 数据打包 -> CAN消息构建 -> 发送到总线
void HAL_CAN_RxFifo0MsgPendingCallback(CAN_HandleTypeDef *hcan)
{
    if (hcan->Instance == CAN1)
  {   
        CAN_RxHeaderTypeDef rxHeader;
        uint8_t data[8];
        
        if (HAL_CAN_GetRxMessage(hcan, CAN_RX_FIFO0, &rxHeader, data) == HAL_OK)
        {
            switch(rxHeader.StdId)
            {
                case MSG_ID_PROPULSOR_CMD_1:					//----对应101
                    ProcessPropulsorCommands(data, 0, 4);
                    break;
                case MSG_ID_PROPULSOR_CMD_2:					//102命令控制
                    ProcessPropulsorCommands(data, 4, 4);
                    break;
                case MSG_ID_CONFIG_1:									//写入操作命令103
                    // 更新前4个CAN ID
                    for(int i = 0; i < 4; i++)
                    {
                        CAN_ID[i] = (uint16_t)(data[i*2] << 8) | data[i*2+1];
                    }
                    Write_CAN_IDs_To_EEPROM();
                    break;
                    
                case MSG_ID_CONFIG_2:									//写入操作命令104
                    // 更新后4个CAN ID
                    for(int i = 0; i < 4; i++)
                    {
                        CAN_ID[i+4] = (uint16_t)(data[i*2] << 8) | data[i*2+1];
                    }
                    Write_CAN_IDs_To_EEPROM();
                    break;
                case MSG_ID_CONFIG_3:
                  // 读取前四个推进器的CANID
                  Read_CAN_IDs_From_EEPROM(0,4);
                  // 增加错误解析
                  {
                    uint8_t error_code[4] = {0x45,0x46,0x00,0x00};
                    SendErrorCAN(CAN_ID[0],error_code,4);
                    SendErrorCAN(CAN_ID[1],error_code,4);
                    SendErrorCAN(CAN_ID[2],error_code,4);
                    SendErrorCAN(CAN_ID[3],error_code,4);
                  }
                  break;
                case MSG_ID_CONFIG_4:
                  // 读取后四个推进器的CANID
                  Read_CAN_IDs_From_EEPROM(4,8);
                  // 增加错误解析
                  {
                    uint8_t error_code[4] = {0x45,0x46,0x00,0x00};
                    SendErrorCAN(CAN_ID[4],error_code,4);
                    SendErrorCAN(CAN_ID[5],error_code,4);
                    SendErrorCAN(CAN_ID[6],error_code,4);
                    SendErrorCAN(CAN_ID[7],error_code,4);
                  }
                  break;
            }
        }
    }
}
//-------------------------------CAN发送程序-----------------------------------//
void SendErrorCAN(uint32_t id, uint8_t *data, uint8_t len)
{

  CAN_SendMessage(id,data,len);
}
void CAN_SendMessage(uint32_t id, uint8_t *data, uint8_t len)
{
    CAN_TxHeaderTypeDef txMessage;
    uint32_t mailbox;
    HAL_StatusTypeDef status;

    txMessage.StdId = id;
    txMessage.ExtId = 0;
    txMessage.RTR = CAN_RTR_DATA;
    txMessage.IDE = CAN_ID_STD;
    txMessage.DLC = len;

    // 发送CAN消息并检查结果
    status = HAL_CAN_AddTxMessage(&hcan1, &txMessage, data, &mailbox);
    if (status != HAL_OK)
    {
        // 发送失败处理
        Error_Handler();
    }

    // 等待发送完成
    while(HAL_CAN_GetTxMailboxesFreeLevel(&hcan1) != 3) {}
}
//---------------------------// 发送命令给推进器的辅助函数-----------------------//
// 发送命令给推进器的辅助函数
void SendCommandToPropulsor(int speed)
{
    uint8_t buf[8] = {0};

    buf[0] = 0x54;
    buf[1] = 0x43;
    buf[2] = 0x00; // 第二个字节保留为0x00
    buf[3] = 0x00; // 第三个字节保留为0x00
    buf[4] = speed >> 24;
    buf[5] = speed >> 16;
    buf[6] = speed >> 8;
    buf[7] = speed;

    CAN_SendMessage(SEND_ID, buf, 8);
}

//---------------------------漏水检测代码-----------------------//
//---------------------------漏水检测代码-----------------------//
void Leakage_Detection(void)
{
    static uint8_t Leakage_Detection_data[8] = {0xFB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
    const uint16_t DEC_PINS[4] = {GPIO_PIN_0, GPIO_PIN_1, GPIO_PIN_2, GPIO_PIN_4};
    const uint8_t PIN_VALUES[4] = {0x01, 0x02, 0x04, 0x08};  // 对应的状态值
    bool status_changed = false;

    // 检查所有传感器
    for(int i = 0; i < 4; i++) {
        if(HAL_GPIO_ReadPin(GPIOA, DEC_PINS[i]) == RESET) {  // 检测到漏水
            Leakage_Detection_data[i + 1] = PIN_VALUES[i];
            status_changed = true;
        } else {
            Leakage_Detection_data[i + 1] = 0x00;
        }
    }

    // 只在状态发生变化时发送CAN消息
    if(status_changed) {
        // 计算校验和
        uint8_t checksum = Leakage_Detection_data[0];
        for(int i = 1; i <= 4; i++) {
            checksum += Leakage_Detection_data[i];
        }
        Leakage_Detection_data[7] = checksum;
        
        CAN_SendMessage(0x123, Leakage_Detection_data, 8);
    }

    HAL_Delay(100);
}
/* USER CODE END 1 */
