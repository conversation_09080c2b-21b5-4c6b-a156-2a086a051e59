#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
CAN1.ABOM=ENABLE
CAN1.AWUM=ENABLE
CAN1.BS1=CAN_BS1_4TQ
CAN1.BS2=CAN_BS2_3TQ
CAN1.CalculateBaudRate=500000
CAN1.CalculateTimeBit=2000
CAN1.CalculateTimeQuantum=250.0
CAN1.IPParameters=CalculateTimeQuantum,CalculateTimeBit,CalculateBaudRate,Prescaler,BS1,BS2,AWUM,ABOM
CAN1.Prescaler=9
CAN2.ABOM=ENABLE
CAN2.AWUM=ENABLE
CAN2.BS1=CAN_BS1_4TQ
CAN2.BS2=CAN_BS2_3TQ
CAN2.CalculateBaudRate=500000
CAN2.CalculateTimeBit=2000
CAN2.CalculateTimeQuantum=250.0
CAN2.IPParameters=CalculateTimeQuantum,CalculateTimeBit,CalculateBaudRate,BS1,BS2,Prescaler,ABOM,AWUM
CAN2.Prescaler=9
File.Version=6
GPIO.groupedBy=
I2C1.ClockSpeed=100000
I2C1.I2C_Mode=I2C_Fast
I2C1.IPParameters=I2C_Mode,ClockSpeed
KeepUserPlacement=false
Mcu.CPN=STM32F407ZET6
Mcu.Family=STM32F4
Mcu.IP0=CAN1
Mcu.IP1=CAN2
Mcu.IP2=I2C1
Mcu.IP3=NVIC
Mcu.IP4=RCC
Mcu.IP5=SYS
Mcu.IP6=TIM14
Mcu.IP7=UART4
Mcu.IPNb=8
Mcu.Name=STM32F407Z(E-G)Tx
Mcu.Package=LQFP144
Mcu.Pin0=PH0-OSC_IN
Mcu.Pin1=PH1-OSC_OUT
Mcu.Pin10=PB6
Mcu.Pin11=PB7
Mcu.Pin12=VP_SYS_VS_Systick
Mcu.Pin13=VP_TIM14_VS_ClockSourceINT
Mcu.Pin2=PA0-WKUP
Mcu.Pin3=PA1
Mcu.Pin4=PB12
Mcu.Pin5=PB13
Mcu.Pin6=PA11
Mcu.Pin7=PA12
Mcu.Pin8=PA13
Mcu.Pin9=PA14
Mcu.PinsNb=14
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407ZETx
MxCube.Version=6.13.0
MxDb.Version=DB.6.0.130
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.CAN1_RX0_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.CAN2_RX1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:false
NVIC.TIM8_TRG_COM_TIM14_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0-WKUP.Mode=Asynchronous
PA0-WKUP.Signal=UART4_TX
PA1.Mode=Asynchronous
PA1.Signal=UART4_RX
PA11.Mode=CAN_Activate
PA11.Signal=CAN1_RX
PA12.Mode=CAN_Activate
PA12.Signal=CAN1_TX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PB12.Mode=CAN_Activate
PB12.Signal=CAN2_RX
PB13.Mode=CAN_Activate
PB13.Signal=CAN2_TX
PB6.Mode=I2C
PB6.Signal=I2C1_SCL
PB7.Mode=I2C
PB7.Signal=I2C1_SDA
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407ZETx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=BSP_CAN_test.ioc
ProjectManager.ProjectName=BSP_CAN_test
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.27
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-MX_GPIO_Init-GPIO-false-HAL-true,2-SystemClock_Config-RCC-false-HAL-false,3-MX_CAN1_Init-CAN1-false-HAL-true,4-MX_CAN2_Init-CAN2-false-HAL-true,5-MX_UART4_Init-UART4-false-HAL-true,6-MX_TIM14_Init-TIM14-false-HAL-true,7-MX_I2C1_Init-I2C1-false-HAL-true
RCC.48MHZClocksFreq_Value=36000000
RCC.AHBFreq_Value=72000000
RCC.APB1CLKDivider=RCC_HCLK_DIV2
RCC.APB1Freq_Value=36000000
RCC.APB1TimFreq_Value=72000000
RCC.APB2Freq_Value=72000000
RCC.APB2TimFreq_Value=72000000
RCC.CortexFreq_Value=72000000
RCC.EthernetFreq_Value=72000000
RCC.FCLKCortexFreq_Value=72000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=72000000
RCC.HSE_VALUE=25000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=192000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSE_VALUE,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=72000000
RCC.PLLCLKFreq_Value=72000000
RCC.PLLM=8
RCC.PLLN=72
RCC.PLLQCLKFreq_Value=36000000
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=12500000
RCC.SYSCLKFreq_VALUE=72000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=384000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOOutputFreq_Value=144000000
RCC.VcooutputI2S=192000000
TIM14.IPParameters=Prescaler,Period
TIM14.Period=30000-1
TIM14.Prescaler=7200-1
UART4.IPParameters=VirtualMode
UART4.VirtualMode=Asynchronous
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM14_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM14_VS_ClockSourceINT.Signal=TIM14_VS_ClockSourceINT
board=custom
