/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    i2c.c
  * @brief   This file provides code for the configuration
  *          of the I2C instances.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "i2c.h"

/* USER CODE BEGIN 0 */
#include "can.h"
/* USER CODE END 0 */
uint16_t CAN_ID[8]={0x301,0x302,0x303,0x304,0x305,0x306,0x307,0x308};
I2C_HandleTypeDef hi2c1;
/* I2C1 init function */
void MX_I2C1_Init(void)
{

  /* USER CODE BEGIN I2C1_Init 0 */

  /* USER CODE END I2C1_Init 0 */

  /* USER CODE BEGIN I2C1_Init 1 */

  /* USER CODE END I2C1_Init 1 */
  hi2c1.Instance = I2C1;
  hi2c1.Init.ClockSpeed = 100000;
  hi2c1.Init.DutyCycle = I2C_DUTYCYCLE_2;
  hi2c1.Init.OwnAddress1 = 0;
  hi2c1.Init.AddressingMode = I2C_ADDRESSINGMODE_7BIT;
  hi2c1.Init.DualAddressMode = I2C_DUALADDRESS_DISABLE;
  hi2c1.Init.OwnAddress2 = 0;
  hi2c1.Init.GeneralCallMode = I2C_GENERALCALL_DISABLE;
  hi2c1.Init.NoStretchMode = I2C_NOSTRETCH_DISABLE;
  if (HAL_I2C_Init(&hi2c1) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN I2C1_Init 2 */

  /* USER CODE END I2C1_Init 2 */

}

void HAL_I2C_MspInit(I2C_HandleTypeDef* i2cHandle)
{

  GPIO_InitTypeDef GPIO_InitStruct = {0};
  if(i2cHandle->Instance==I2C1)
  {
  /* USER CODE BEGIN I2C1_MspInit 0 */

  /* USER CODE END I2C1_MspInit 0 */

    __HAL_RCC_GPIOB_CLK_ENABLE();
    /**I2C1 GPIO Configuration
    PB6     ------> I2C1_SCL
    PB7     ------> I2C1_SDA
    */
    GPIO_InitStruct.Pin = GPIO_PIN_6|GPIO_PIN_7;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_OD;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF4_I2C1;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    /* I2C1 clock enable */
    __HAL_RCC_I2C1_CLK_ENABLE();
  /* USER CODE BEGIN I2C1_MspInit 1 */

  /* USER CODE END I2C1_MspInit 1 */
  }
}

void HAL_I2C_MspDeInit(I2C_HandleTypeDef* i2cHandle)
{

  if(i2cHandle->Instance==I2C1)
  {
  /* USER CODE BEGIN I2C1_MspDeInit 0 */

  /* USER CODE END I2C1_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_I2C1_CLK_DISABLE();

    /**I2C1 GPIO Configuration
    PB6     ------> I2C1_SCL
    PB7     ------> I2C1_SDA
    */
    HAL_GPIO_DeInit(GPIOB, GPIO_PIN_6);

    HAL_GPIO_DeInit(GPIOB, GPIO_PIN_7);

  /* USER CODE BEGIN I2C1_MspDeInit 1 */

  /* USER CODE END I2C1_MspDeInit 1 */
  }
}

/* USER CODE BEGIN 1 */


//产生起始信号
void IIC_Start(void)
{
   hi2c1.Instance->CR1|=1<<8;
   while(!(hi2c1.Instance->SR1&1<<0)){}//等待起始信号发送成功
   hi2c1.Instance->CR1&=~(1<<8);
}
//停止信号
void IIC_Stop(void)
{
	hi2c1.Instance->CR1|=1<<9;
}
//发送数据
void IIC_WriteData(uint8_t data)
{
    uint32_t startTick = HAL_GetTick();
    hi2c1.Instance->DR = data;  // 发送数据
    while (!(hi2c1.Instance->SR1 & 1 << 7)) {  // 等待数据发送完成
        if ((HAL_GetTick() - startTick) > IIC_TIMEOUT_MS) {
            Error_Handler();  // 处理超时错误
            break;
        }
    }
}
/*发送地址*/
void IIC_WriteAddr(uint8_t adrr)
{
	uint8_t stat;
	hi2c1.Instance->DR=adrr;
	while(!(hi2c1.Instance->SR1&1<<1)){}//等待数据发送完成
	stat=hi2c1.Instance->SR2;//对SR2读取清除标志位
}
uint8_t IIC_readData(void)
{
    uint32_t startTick = HAL_GetTick();
    uint8_t data;
    hi2c1.Instance->CR1 |= 1 << 10;  // 启用应答
    while (!(hi2c1.Instance->SR1 & 1 << 6)) {  // 等待数据接收完成
        if ((HAL_GetTick() - startTick) > IIC_TIMEOUT_MS) {
            Error_Handler();  // 处理超时错误
            return 0;  // 返回默认值或错误代码
        }
    }
    data = hi2c1.Instance->DR;
    hi2c1.Instance->CR1 &= ~(1 << 10);  // 禁用应答
    return data;
}

/*写一个字节函数*/
void AT24C08_WriteOneByte(uint8_t addr,uint8_t data)
{
	IIC_Start();//发送起始信号
	IIC_WriteAddr(AT24C08_ADDR_W);//发送地址
	IIC_WriteData(addr);//发送写入数据地址
	IIC_WriteData(data);//写入数据
	IIC_Stop();//停止信号
	HAL_Delay(10);//写周期时间
}
/*读一个字节函数*/
uint8_t AT24C08_ReadOneByte(uint8_t addr)
{
	uint8_t data;  
	IIC_Start();//发送起始信号
	IIC_WriteAddr(AT24C08_ADDR_W);//器件地址+写使能
	IIC_WriteData(addr);//发送写入数据地址
	IIC_Start();//发送起始信号
	IIC_WriteAddr(AT24C08_ADDR_R);//器件地址+读使能
	data=IIC_readData();//读取一个字节数据
	IIC_Stop();//停止信号
	return data;
}
// 写入数据到AT24C08
void AT24C08_WriteData(uint8_t addr, uint8_t *data, uint16_t length)
{
    IIC_Start();                         // 发送起始信号
    IIC_WriteAddr(AT24C08_ADDR_W);       // 发送设备地址（写）
    IIC_WriteData(addr);                 // 发送数据起始地址
    
    // 循环写入数据
    for (uint16_t i = 0; i < length; i++) {
        IIC_WriteData(data[i]);           // 写入每个字节数据
    }

    IIC_Stop();                          // 停止信号
    HAL_Delay(10);                       // 延迟，确保数据写入完成
}// 从AT24C08读取数据
void AT24C08_ReadData(uint8_t addr, uint8_t *data, uint16_t length)
{
    IIC_Start();                         // 发送起始信号
    IIC_WriteAddr(AT24C08_ADDR_W);       // 发送设备地址（写）
    IIC_WriteData(addr);                 // 发送数据起始地址

    IIC_Start();                         // 发送重复的起始信号
    IIC_WriteAddr(AT24C08_ADDR_R);       // 发送设备地址（读）

    // 循环读取数据
    for (uint16_t i = 0; i < length; i++) {
        data[i] = IIC_readData();        // 读取一个字节的数据
    }

    IIC_Stop();                          // 停止信号
}
// 写入CAN ID到EEPROM
HAL_StatusTypeDef Write_CAN_IDs_To_EEPROM(void)
{
    uint8_t temp[16];
    uint8_t verify[16];
    // 将uint16_t的CAN_ID转换为uint8_t数组
    for(int i = 0; i < 8; i++)
    {
        // 正确提取高字节和低字节
        temp[i*2] = (uint8_t)((CAN_ID[i] >> 8) & 0xFF);    // 高字节
        temp[i*2 + 1] = (uint8_t)(CAN_ID[i] & 0xFF);       // 低字节
		}
    
    // 写入数据
    AT24C08_WriteData(0x00, temp, 16);
    HAL_Delay(10); // 等待写入完成
    
    // 读回验证
    AT24C08_ReadData(0x00, verify, 16);
    for(int i = 0; i < 16; i++) {
        printf("Byte[%d]: Wrote 0x%02X, Read 0x%02X\r\n", i, temp[i], verify[i]);
        if(temp[i] != verify[i]) {
            printf("Verification failed at byte %d!\r\n", i);
            return HAL_ERROR;
        }
    }
    
    printf("EEPROM write successful!\r\n");
    return HAL_OK;
}

// 从EEPROM读取CAN ID
void Read_CAN_IDs_From_EEPROM(uint8_t start_index, uint8_t end_index)
{
    if (start_index >= 8 || end_index > 8 || start_index >= end_index) {
        return; // 参数检查
    }

    uint8_t temp[16];
    //计算一下实际需要读取的字节数
    uint8_t length = (end_index - start_index) * 2;
    //计算EEPROM中的起始地址
    uint8_t start_addr = start_index * 2;
    
    // 只读取需要的部分
    AT24C08_ReadData(start_addr, temp, length);
    
    // 将读取的数据转换回uint16_t数组
    for(int i = 0; i < (end_index - start_index); i++)
    {
        CAN_ID[start_index + i] = ((uint16_t)temp[i*2] << 8) | temp[i*2 + 1];
        printf("Read CAN_ID[%d] = 0x%04X\r\n", start_index + i, CAN_ID[start_index + i]);
    }
}
// 修改EEPROM中特定位置的CAN ID
HAL_StatusTypeDef Update_Single_CAN_ID_In_EEPROM(uint8_t index, uint16_t new_id)
{
    //检查索引是否有效
    if(!IS_VALID_CAN_ID_INDEX(index)) {
        return HAL_ERROR;
    }
    //计算EEPROM中的起始地址，每个CAN ID占用2字节
    uint8_t addr = index * CAN_ID_BYTES_PER_ID;
    //定义临时变量
    uint8_t temp[2];
    uint8_t verify[2];
    
    // 将新的ID拆分为两个字节
    temp[0] = 0x00;  // 高字节为0
    temp[1] = (uint8_t)(new_id & 0xFF);  // 低字节
    
    // 写入EEPROM
    AT24C08_WriteData(addr, temp, 2);
    HAL_Delay(10); // 等待写入完成
    
    // 读回验证
    AT24C08_ReadData(addr, verify, 2);
    // 验证数据
    if(verify[0] != temp[0] || verify[1] != temp[1]) {
        return HAL_ERROR;
    }
    
    // 更新内存中的CAN_ID数组
    CAN_ID[index] = (unsigned char)new_id;
    return HAL_OK;
}
/* USER CODE END 1 */
